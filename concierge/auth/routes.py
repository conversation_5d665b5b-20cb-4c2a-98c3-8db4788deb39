from flask import (
    Blueprint, render_template, request, jsonify, session, redirect, url_for, flash, g, make_response
)
from datetime import datetime

# Import token verification from utils
from .utils import verify_token
# Import Firestore operations
from concierge.utils.firestore_client import get_user, create_user, update_user, find_user_by_phone

# Create Blueprint
auth_bp = Blueprint('auth', __name__, template_folder='../templates') # Point to parent templates folder

@auth_bp.route('/login', methods=['GET'])
def login():
    """Displays the login page."""
    if 'user_id' in session:
        # If user is already logged in, redirect to dashboard
        return redirect(url_for('views.dashboard')) # Assumes dashboard is in 'views' blueprint
    # Render the main index page which contains the login form
    return render_template('index.html')

@auth_bp.route('/phone-login', methods=['GET', 'POST'])
def phone_login():
    """Handle standalone phone login flow."""
    if request.method == 'GET':
        # Show phone entry form
        return render_template('phone_login.html')
    
    # Handle POST - process phone number
    try:
        phone_number = request.form.get('phone_number', '').strip()
        
        if not phone_number:
            flash('Phone number is required.', 'error')
            return render_template('phone_login.html')
        
        # Validate and clean phone number
        from concierge.utils.phone_utils import validate_phone_number, clean_phone_for_storage
        
        if not validate_phone_number(phone_number):
            flash('Please enter a valid phone number.', 'error')
            return render_template('phone_login.html', phone_number=phone_number)
        
        clean_phone = clean_phone_for_storage(phone_number)
        
        # Find user by phone number
        user_data = find_user_by_phone(clean_phone)
        
        if user_data and not user_data.get('isTemporary', False):
            # Permanent user found
            has_pin = bool(user_data.get('pinCode'))
            
            if has_pin:
                # User Found + Has PIN → PIN Entry
                session['phone_login_data'] = {
                    'phone_number': clean_phone,
                    'user_id': user_data['id'],
                    'step': 'pin_entry'
                }
                return redirect(url_for('auth.pin_entry'))
            else:
                # User Found + No PIN → OTP Login → Prompt to Set PIN
                session['phone_login_data'] = {
                    'phone_number': clean_phone,
                    'user_id': user_data['id'],
                    'step': 'otp_login'
                }
                return redirect(url_for('auth.otp_login'))
        else:
            # User Not Found → Signup Flow
            session['signup_data'] = {
                'phone_number': clean_phone,
                'step': 'account_type_selection'
            }
            return redirect(url_for('auth.signup_choice'))
    
    except Exception as e:
        print(f"Error in phone login: {e}")
        flash('An error occurred. Please try again.', 'error')
        return render_template('phone_login.html')

@auth_bp.route('/pin-entry', methods=['GET', 'POST'])
def pin_entry():
    """Handle PIN entry for permanent users."""
    login_data = session.get('phone_login_data')
    if not login_data or login_data.get('step') != 'pin_entry':
        return redirect(url_for('auth.phone_login'))
    
    if request.method == 'GET':
        return render_template('pin_entry.html', 
                             phone_number=login_data.get('phone_number'),
                             attempts=login_data.get('pin_attempts', 0))
    
    # Handle POST - verify PIN
    try:
        entered_pin = request.form.get('pin', '').strip()
        
        if not entered_pin or len(entered_pin) != 4 or not entered_pin.isdigit():
            flash('Please enter a 4-digit PIN.', 'error')
            return render_template('pin_entry.html', 
                                 phone_number=login_data.get('phone_number'),
                                 attempts=login_data.get('pin_attempts', 0))
        
        # Verify PIN
        from concierge.utils.firestore_client import verify_user_pin
        user_id = login_data.get('user_id')
        
        if verify_user_pin(user_id, entered_pin):
            # PIN correct - create session and redirect to dashboard
            session.pop('phone_login_data', None)
            session['user_id'] = user_id
            session['user_role'] = 'guest'  # Will be updated from user data
            flash('Login successful!', 'success')
            return redirect(url_for('views.dashboard'))
        else:
            # PIN incorrect
            attempts = login_data.get('pin_attempts', 0) + 1
            login_data['pin_attempts'] = attempts
            session['phone_login_data'] = login_data
            
            if attempts >= 3:
                # 3 attempts failed → OTP Recovery
                flash('Too many incorrect attempts. Please verify with OTP.', 'warning')
                login_data['step'] = 'otp_recovery'
                session['phone_login_data'] = login_data
                return redirect(url_for('auth.otp_recovery'))
            else:
                flash(f'Incorrect PIN. {3-attempts} attempts remaining.', 'error')
                return render_template('pin_entry.html', 
                                     phone_number=login_data.get('phone_number'),
                                     attempts=attempts)
    
    except Exception as e:
        print(f"Error in PIN entry: {e}")
        flash('An error occurred. Please try again.', 'error')
        return render_template('pin_entry.html', 
                             phone_number=login_data.get('phone_number'),
                             attempts=login_data.get('pin_attempts', 0))

@auth_bp.route('/otp-login', methods=['GET'])
def otp_login():
    """Show OTP login form for users without PIN."""
    login_data = session.get('phone_login_data')
    if not login_data or login_data.get('step') != 'otp_login':
        return redirect(url_for('auth.phone_login'))
    
    return render_template('otp_verification.html', 
                         phone_number=login_data.get('phone_number'),
                         next_step='set_pin',
                         form_action=url_for('auth.complete_phone_auth'))

@auth_bp.route('/otp-recovery', methods=['GET'])
def otp_recovery():
    """Show OTP recovery form for failed PIN attempts."""
    login_data = session.get('phone_login_data')
    if not login_data or login_data.get('step') != 'otp_recovery':
        return redirect(url_for('auth.phone_login'))
    
    return render_template('otp_verification.html', 
                         phone_number=login_data.get('phone_number'),
                         next_step='dashboard',
                         recovery_mode=True,
                         form_action=url_for('auth.complete_phone_auth'))

@auth_bp.route('/signup-choice', methods=['GET', 'POST'])
def signup_choice():
    """Handle account type selection for new users."""
    signup_data = session.get('signup_data')
    if not signup_data or signup_data.get('step') != 'account_type_selection':
        return redirect(url_for('auth.phone_login'))
    
    if request.method == 'GET':
        return render_template('account_type_selection.html', 
                             phone_number=signup_data.get('phone_number'),
                             form_action=url_for('auth.signup_choice'))
    
    # Handle POST - process account type selection
    account_type = request.form.get('account_type')
    
    if account_type == 'guest':
        # Guest Account → Prompt for magic link from host
        return render_template('guest_magic_link_prompt.html',
                             phone_number=signup_data.get('phone_number'))
    elif account_type == 'host':
        # Host Account → OTP verification then property setup
        signup_data['account_type'] = 'host'
        signup_data['step'] = 'otp_verification'
        session['signup_data'] = signup_data
        return render_template('otp_verification.html',
                             phone_number=signup_data.get('phone_number'),
                             account_type='host',
                             form_action=url_for('auth.complete_phone_auth'))
    else:
        flash('Please select an account type.', 'error')
        return render_template('account_type_selection.html', 
                             phone_number=signup_data.get('phone_number'),
                             form_action=url_for('auth.signup_choice'))

@auth_bp.route('/complete-phone-auth', methods=['POST'])
def complete_phone_auth():
    """Complete phone authentication after OTP verification."""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        id_token = data.get('idToken')
        firebase_phone = data.get('phoneNumber')
        firebase_uid = data.get('uid')
        
        if not all([id_token, firebase_phone, firebase_uid]):
            return jsonify({'error': 'Missing verification data'}), 400
        
        # Verify Firebase token
        from firebase_admin import auth as admin_auth
        try:
            decoded_token = admin_auth.verify_id_token(id_token)
            if decoded_token['uid'] != firebase_uid:
                return jsonify({'error': 'Token verification failed'}), 400
        except Exception as e:
            print(f"Firebase token verification failed: {e}")
            return jsonify({'error': 'Invalid verification token'}), 400
        
        # Handle different scenarios
        login_data = session.get('phone_login_data')
        signup_data = session.get('signup_data')
        
        if login_data:
            # Handle login scenarios
            return handle_login_completion(login_data, firebase_uid, firebase_phone)
        elif signup_data:
            # Handle signup scenarios
            return handle_signup_completion(signup_data, firebase_uid, firebase_phone)
        else:
            return jsonify({'error': 'No authentication context found'}), 400
    
    except Exception as e:
        print(f"Error completing phone auth: {e}")
        return jsonify({'error': 'Authentication failed'}), 500

def handle_login_completion(login_data, firebase_uid, firebase_phone):
    """Handle completion of login flows."""
    user_id = login_data.get('user_id')
    step = login_data.get('step')
    
    if step == 'otp_login':
        # User logged in without PIN → Check if they should set up a PIN
        from concierge.utils.firestore_client import get_user
        user_data = get_user(user_id)
        
        session.pop('phone_login_data', None)
        session['user_id'] = user_id
        session['user_role'] = user_data.get('role', 'guest') if user_data else 'guest'
        
        # Check if this user has never set up a PIN (new permanent user)
        has_pin = user_data.get('pinCode') if user_data else False
        if not has_pin:
            # This is a permanent user without any PIN → prompt for PIN creation
            return jsonify({
                'success': True,
                'message': 'Login successful',
                'redirect': url_for('views.dashboard'),
                'new_permanent_user': True,  # Signal this is a permanent user without PIN
                'prompt_pin_creation': True  # Signal to show PIN creation prompt
            })
        else:
            # User has a PIN already, just login normally
            # The dashboard will handle showing warnings for users with default PINs
            return jsonify({
                'success': True,
                'message': 'Login successful',
                'redirect': url_for('views.dashboard')
            })
    
    elif step == 'otp_recovery':
        # PIN recovery successful
        session.pop('phone_login_data', None)
        session['user_id'] = user_id
        session['user_role'] = 'guest'
        
        return jsonify({
            'success': True,
            'message': 'Account recovered successfully',
            'redirect': url_for('views.dashboard')
        })
    
    return jsonify({'error': 'Invalid login step'}), 400

def handle_signup_completion(signup_data, firebase_uid, firebase_phone):
    """Handle completion of signup flows."""
    account_type = signup_data.get('account_type')
    phone_number = signup_data.get('phone_number')
    
    if account_type == 'host':
        # Create new host account with default PIN (last 4 digits of phone)
        from concierge.utils.phone_utils import get_last_4_digits
        default_pin = get_last_4_digits(firebase_phone)
        
        user_data = {
            'uid': firebase_uid,
            'phoneNumber': firebase_phone,
            'role': 'host',
            'displayName': 'New Host',
            'accountType': 'permanent',
            'pinCode': default_pin,  # Set default PIN to last 4 digits
            'createdAt': datetime.now().isoformat(),
            'isTemporary': False,
            'hasDefaultPin': True  # Flag to indicate this user has a default PIN
        }
        
        success = create_user(firebase_uid, user_data)
        if success:
            session.pop('signup_data', None)
            session['user_id'] = firebase_uid
            session['user_role'] = 'host'
            session['setup_property_required'] = True
            
            return jsonify({
                'success': True,
                'message': 'Host account created successfully',
                'redirect': url_for('views.dashboard'),
                'setup_property_required': True,
                'has_default_pin': True  # Signal that user has default PIN and should change it
            })
        else:
            return jsonify({'error': 'Failed to create account'}), 500
    
    elif account_type == 'guest':
        # Create new guest account without PIN (prompt for PIN creation)
        user_data = {
            'uid': firebase_uid,
            'phoneNumber': firebase_phone,
            'role': 'guest',
            'displayName': 'New Guest',
            'accountType': 'permanent',
            'createdAt': datetime.now().isoformat(),
            'isTemporary': False
            # No pinCode field - user hasn't set up a PIN yet
        }
        
        success = create_user(firebase_uid, user_data)
        if success:
            session.pop('signup_data', None)
            session['user_id'] = firebase_uid
            session['user_role'] = 'guest'
            
            return jsonify({
                'success': True,
                'message': 'Guest account created successfully',
                'redirect': url_for('views.dashboard'),
                'new_permanent_user': True,  # Signal this is a new permanent user
                'prompt_pin_creation': True  # Signal to show PIN creation prompt
            })
        else:
            return jsonify({'error': 'Failed to create account'}), 500
    
    return jsonify({'error': 'Invalid signup type'}), 400

@auth_bp.route('/logout')
def logout():
    """Logs the user out by clearing the session."""
    user_id = session.get('user_id')
    session.clear()
    print(f"User {user_id} logged out.") # DEBUG
    flash('You have been logged out.', 'info')
    
    # Also clear magic link session cookie if present
    response = make_response(redirect(url_for('auth.login')))
    
    # Clear magic link session cookie
    response.set_cookie(
        'magicLinkSession',
        '',
        expires=0,
        httponly=True,
        secure=False,
        samesite='Lax'
    )
    
    return response

@auth_bp.route('/verify-token', methods=['POST'])
def verify_token_route():
    """Verifies the authentication token received from the client."""
    id_token = request.json.get('idToken')
    if not id_token:
        print("Verify token request missing idToken.") # DEBUG
        return jsonify({"success": False, "error": "ID token is required."}), 400

    try:
        print(f"Received token for verification: {id_token[:10]}...") # DEBUG: Log partial token
        # Replace Firebase token verification with our new verify_token function
        decoded_token = verify_token(id_token)
        if not decoded_token:
             # verify_token logs the specific error
             print("Token verification failed (verify_token returned None).") # DEBUG
             return jsonify({"success": False, "error": "Invalid or expired token."}), 401

        uid = decoded_token['uid']
        print(f"Token verified successfully for UID: {uid}") # DEBUG

        # --- User Role and Profile Handling ---
        # Get user from Firestore
        user_data = get_user(uid)
        user_role = 'guest' # Default role

        if user_data:
            # User exists, get role from Firestore
            user_role = user_data.get('role', 'guest')
            print(f"Existing user {uid} found with role: {user_role}") # DEBUG
            # Update last login time
            update_user(uid, {'lastLogin': datetime.now().isoformat()})
        else:
            # New user registration - check if it's a host based on email domain or other logic
            print(f"New user detected with UID: {uid}. Creating user record...") # DEBUG

            # Get email and phone from the token if available
            email = decoded_token.get('email')
            phone_number = decoded_token.get('phone_number')

            # For now, let's default all new users to 'guest' unless specific logic is added
            user_role = 'guest'

            # Create user profile in Firestore
            print(f"Creating new user profile for {uid} with role: {user_role}") # DEBUG
            create_success = create_user(uid, {
                'uid': uid,
                'email': email, # Store email if available
                'phoneNumber': phone_number, # Store phone if available
                'role': user_role,
                'displayName': decoded_token.get('name', 'New User')
            })

            if not create_success:
                print(f"ERROR: Failed to create user {uid} in Firestore") # DEBUG
                return jsonify({"success": False, "error": "Failed to create user account."}), 500

            # Verify user was actually created by trying to get it
            user_data = get_user(uid)
            if not user_data:
                print(f"ERROR: User {uid} not found in Firestore after creation") # DEBUG
                return jsonify({"success": False, "error": "User account creation verification failed."}), 500

            print(f"User {uid} successfully created and verified in Firestore") # DEBUG

        # --- Session Management ---
        session.permanent = True # Use the permanent session lifetime from app config
        session['user_id'] = uid
        session['user_role'] = user_role # Store role in session
        g.user_id = uid # Set g.user_id for the current request context
        g.user_role = user_role

        print(f"Session created for user {uid}, role {user_role}. Session keys: {list(session.keys())}") # DEBUG
        return jsonify({"success": True, "message": "Token verified successfully.", "role": user_role})

    except Exception as e:
        print(f"An unexpected error occurred during token verification: {e}") # DEBUG
        # Log the full traceback for unexpected errors
        import traceback
        traceback.print_exc()
        return jsonify({"success": False, "error": "An unexpected error occurred."}), 500
