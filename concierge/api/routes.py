from flask import Blueprint, jsonify, request, session, current_app, abort, g
import json
import traceback
import os
from datetime import datetime, timezone, timedelta # Import datetime for ISO format conversion

# --- Imports from our modules ---
from concierge.auth.utils import login_required # Get decorator
from concierge.api.utils import generate_qna_with_gemini # Import Gemini Q&A generation function
from concierge.utils.reservations import update_all_reservations, fetch_and_parse_ical

# Import Firestore client functions
from concierge.utils.firestore_client import (
    get_user, get_property, list_properties_by_host, update_property,
    create_property, delete_property, update_property, update_user,
    create_knowledge_source, list_knowledge_sources,
    create_knowledge_item, list_knowledge_items_by_property, list_knowledge_items_by_source,
    get_knowledge_item, update_knowledge_item, update_knowledge_item_status,
    delete_knowledge_item, find_similar_knowledge_items, generate_embedding,
    list_property_reservations, update_reservation_phone,
    create_magic_link, list_magic_links_by_reservation, revoke_magic_link,
    get_reservation, generate_magic_link_url
)

# Import Firestore AI helpers
from concierge.utils.firestore_ai_helpers import (
    process_query_with_rag, process_query_with_tools
)
# Import specific config variables or the whole config module
from concierge.config import LAMBDA_CLIENT
# Import Gemini variables from utils.gemini_config
from concierge.utils.gemini_config import genai_enabled, gemini_model

# --- Temporary: Define helper functions here if not imported ---
# TODO: Move these function definitions to appropriate util files (e.g., utils/file_processing.py, utils/ai_helpers.py, utils/aws.py)

def extract_text_from_file(file_path):
    # Placeholder implementation - Copy the actual logic from app.py
    print(f"Placeholder: Extracting text from {file_path}")
    _, file_extension = os.path.splitext(file_path)
    if file_extension.lower() == '.txt':
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    elif file_extension.lower() in ['.pdf', '.docx']:
         # Add actual pdf/docx extraction logic here (using PyPDF2, python-docx)
         print(f"Warning: PDF/DOCX extraction logic not implemented in placeholder for {file_path}")
         return f"Extracted text from {os.path.basename(file_path)}" # Dummy text
    else:
        print(f"Unsupported file type for text extraction: {file_extension}")
        return None

def generate_qna(knowledge_base_text):
    # Placeholder implementation - Copy the actual logic from app.py
    print(f"Placeholder: Generating Q&A for text length: {len(knowledge_base_text)}")
    if not genai_enabled or not gemini_model:
        print("Gemini not enabled or model not initialized.")
        return [] # Return empty list or raise error

    prompt = f"""
    Generate a list of question and answer pairs based on the following text. The questions should be things a guest might ask, and the answers should be directly derived from the text. Format the output as a JSON list of objects, where each object has a "question" and "answer" key.

    Text:
    {knowledge_base_text}

    JSON Output:
    """
    try:
        response = gemini_model.generate_content(prompt)
        # Basic cleanup attempt
        cleaned_response = response.text.strip().lstrip('```json').rstrip('```')
        qna_list = json.loads(cleaned_response)
        if not isinstance(qna_list, list):
             raise ValueError("Generated response is not a JSON list.")
        # Further validation could be added here (check for question/answer keys)
        print(f"Generated {len(qna_list)} Q&A pairs.")
        return qna_list
    except json.JSONDecodeError as e:
        print(f"Error decoding Gemini JSON response: {e}\nRaw response:\n{response.text}")
        return [{"question": "Error generating Q&A", "answer": "Could not parse the AI response."}]
    except Exception as e:
        print(f"Error generating Q&A with Gemini: {e}")
        # Log traceback for detailed debugging
        traceback.print_exc()
        return [{"question": "Error generating Q&A", "answer": f"An error occurred: {e}"}]

# --- End Temporary Helper Functions ---


# Create Blueprint
api_bp = Blueprint('api', __name__, url_prefix='/api')

@api_bp.route('/gemini-voice-config', methods=['GET'])
def get_gemini_voice_config():
    """Provides configuration needed for Gemini Voice frontend, including the API key."""
    # Check if user is authenticated via regular login or magic link session
    user_id = session.get('user_id')
    magic_link_session = request.cookies.get('magicLinkSession')

    # Allow access for regular authenticated users or magic link users
    if not user_id and not magic_link_session:
        current_app.logger.error("Gemini voice config requested without authentication")
        return jsonify({"error": "Authentication required"}), 401

    # Validate magic link session if present
    if magic_link_session and not user_id:
        try:
            from concierge.utils.session_manager import validate_session
            is_valid, temp_user_id, reason = validate_session(magic_link_session)
            if not is_valid:
                current_app.logger.error(f"Invalid magic link session for voice config: {reason}")
                return jsonify({"error": "Invalid session"}), 401
            user_id = temp_user_id  # Use temporary user ID
            current_app.logger.info(f"Magic link user {temp_user_id} requesting voice config")
        except Exception as e:
            current_app.logger.error(f"Error validating magic link session: {e}")
            return jsonify({"error": "Session validation error"}), 401

    api_key = os.getenv('GEMINI_API_KEY')

    if not api_key:
        current_app.logger.error(f"User {user_id} requested Gemini voice config, but GEMINI_API_KEY is not set on the server.")
        return jsonify({"error": "Voice service configuration missing on server."}), 500

    # Return only the necessary config (just the key for now)
    return jsonify({
        "apiKey": api_key
        # Add any other necessary config here in the future
    })

@api_bp.route('/user/profile', methods=['GET'])
def get_user_profile_universal():
    """Gets the profile of the currently logged-in user (supports both regular and magic link sessions)."""
    # Check if user is authenticated via regular login or magic link session
    user_id = session.get('user_id')
    magic_link_session = request.cookies.get('magicLinkSession')

    # Allow access for regular authenticated users or magic link users
    if not user_id and not magic_link_session:
        current_app.logger.error("User profile requested without authentication")
        return jsonify({"error": "Authentication required"}), 401

    # Validate magic link session if present
    if magic_link_session and not user_id:
        try:
            from concierge.utils.session_manager import validate_session
            is_valid, temp_user_id, reason = validate_session(magic_link_session)
            if not is_valid:
                current_app.logger.error(f"Invalid magic link session for profile: {reason}")
                return jsonify({"error": "Invalid session"}), 401
            user_id = temp_user_id  # Use temporary user ID
            current_app.logger.info(f"Magic link user {temp_user_id} requesting profile")
        except Exception as e:
            current_app.logger.error(f"Error validating magic link session: {e}")
            return jsonify({"error": "Session validation error"}), 401

    try:
        # Check if this is a temporary user
        if user_id.startswith('temp_magic_'):
            # Get temporary user data
            from concierge.utils.firestore_client import get_temporary_user
            temp_user = get_temporary_user(user_id)

            if temp_user:
                # Return temporary user profile data
                profile_data = {
                    'displayName': temp_user.get('displayName', ''),
                    'email': temp_user.get('email', ''),
                    'phoneNumber': f"***-***-{temp_user.get('phoneNumberLast4', '****')}",
                    'role': 'temporary_guest',
                    'isTemporary': True
                }

                return jsonify({
                    "success": True,
                    "user": profile_data
                })
            else:
                return jsonify({"error": "Temporary user profile not found"}), 404
        else:
            # Regular user - get from Firestore
            from concierge.utils.firestore_client import get_user
            user_data = get_user(user_id)

            if user_data:
                # Return only safe profile data
                profile_data = {
                    'displayName': user_data.get('displayName') or user_data.get('DisplayName') or '',
                    'email': user_data.get('email') or user_data.get('Email') or '',
                    'phoneNumber': user_data.get('phoneNumber') or user_data.get('PhoneNumber') or '',
                    'role': user_data.get('role', 'guest'),
                    'isTemporary': False
                }

                return jsonify({
                    "success": True,
                    "user": profile_data
                })
            else:
                return jsonify({"error": "User profile not found"}), 404

    except Exception as e:
        print(f"Error fetching profile for user {user_id}: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({"error": f"Server error fetching profile: {e}"}), 500

@api_bp.route('/profile', methods=['PUT'])
def update_profile_universal():
    """Updates the profile of the currently logged-in user (supports both regular and magic link sessions)."""
    # Check if user is authenticated via regular login or magic link session
    user_id = session.get('user_id')
    magic_link_session = request.cookies.get('magicLinkSession')

    # Allow access for regular authenticated users or magic link users
    if not user_id and not magic_link_session:
        current_app.logger.error("Profile update requested without authentication")
        return jsonify({"error": "Authentication required"}), 401

    # Validate magic link session if present
    if magic_link_session and not user_id:
        try:
            from concierge.utils.session_manager import validate_session
            is_valid, temp_user_id, reason = validate_session(magic_link_session)
            if not is_valid:
                current_app.logger.error(f"Invalid magic link session for profile update: {reason}")
                return jsonify({"error": "Invalid session"}), 401
            user_id = temp_user_id  # Use temporary user ID
            current_app.logger.info(f"Magic link user {temp_user_id} updating profile")
        except Exception as e:
            current_app.logger.error(f"Error validating magic link session: {e}")
            return jsonify({"error": "Session validation error"}), 401

    data = request.get_json()
    if not data:
        return jsonify({"error": "No data provided"}), 400

    try:
        # Check if this is a temporary user
        if user_id.startswith('temp_magic_'):
            # Handle temporary user profile update
            from concierge.utils.firestore_client import get_temporary_user, update_temporary_user_name

            # For temporary users, enforce phone number requirement when adding email
            email_provided = 'email' in data and data['email']
            phone_number_provided = 'phoneNumber' in data and data['phoneNumber']
            display_name_provided = 'displayName' in data and data['displayName']

            # If user is trying to add email without phone number, require both
            if email_provided and not phone_number_provided:
                return jsonify({
                    "error": "Phone number is required when adding email to temporary account",
                    "validation_error": "phone_required_with_email"
                }), 400

            # Allow display name updates without requiring phone/email
            update_data = {}

            if display_name_provided:
                update_data['displayName'] = data['displayName']

            if email_provided:
                update_data['email'] = data['email']

            if update_data:
                # Update temporary user data
                temp_user = get_temporary_user(user_id)
                if temp_user:
                    # Update the temporary user document
                    from concierge.utils.firestore_client import get_firestore_client
                    db = get_firestore_client()
                    temp_user_ref = db.collection('temporary_users').document(user_id)
                    temp_user_ref.update(update_data)

                    response_data = {
                        "success": True,
                        "message": "Profile updated successfully",
                        "updated_fields": list(update_data.keys())
                    }
                    
                    # If phone number was provided, indicate that OTP verification is needed
                    if phone_number_provided:
                        response_data["phone_verification_required"] = True
                        response_data["phone_number"] = data['phoneNumber']
                    
                    return jsonify(response_data)
                else:
                    return jsonify({"error": "Temporary user not found"}), 404
            elif phone_number_provided:
                # Only phone number provided, return success and indicate OTP verification needed
                return jsonify({
                    "success": True,
                    "message": "Phone verification required",
                    "phone_verification_required": True,
                    "phone_number": data['phoneNumber'],
                    "updated_fields": []
                })
            else:
                return jsonify({"error": "No valid fields to update"}), 400
        else:
            # Regular user - update in Firestore
            from concierge.utils.firestore_client import update_user

            # Prepare update data
            update_data = {}

            if 'displayName' in data:
                update_data['displayName'] = data['displayName']

            if 'email' in data:
                update_data['email'] = data['email']

            if 'phoneNumber' in data:
                update_data['phoneNumber'] = data['phoneNumber']

            if update_data:
                # Update the user's profile in Firestore
                success = update_user(user_id, update_data)

                if success:
                    # Update session data if display name was changed
                    if 'displayName' in update_data:
                        session['guest_name'] = update_data['displayName']

                    return jsonify({
                        "success": True,
                        "message": "Profile updated successfully",
                        "updated_fields": list(update_data.keys())
                    })
                else:
                    return jsonify({"error": "Failed to update profile"}), 500
            else:
                return jsonify({"error": "No valid fields to update"}), 400

    except Exception as e:
        print(f"Error updating profile for user {user_id}: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({"error": f"Server error updating profile: {e}"}), 500

@api_bp.route('/firebase-config', methods=['GET'])
def get_firebase_config():
    """Provides Firebase configuration (public endpoint for login functionality)."""
    try:
        # DEBUG: Log that this endpoint is being called without authentication
        current_app.logger.info("Firebase config endpoint called - NO LOGIN REQUIRED")
        
        # Get Firebase configuration from environment variables
        firebase_config = {
            "apiKey": os.getenv('FIREBASE_API_KEY'),
            "authDomain": os.getenv('FIREBASE_AUTH_DOMAIN'),  
            "projectId": os.getenv('FIREBASE_PROJECT_ID'),
            "storageBucket": os.getenv('FIREBASE_STORAGE_BUCKET'),
            "messagingSenderId": os.getenv('FIREBASE_MESSAGING_SENDER_ID'),
            "appId": os.getenv('FIREBASE_APP_ID'),
            "measurementId": os.getenv('FIREBASE_MEASUREMENT_ID', '')
        }
        
        # Check that required fields are present
        required_fields = ['apiKey', 'authDomain', 'projectId', 'storageBucket', 'messagingSenderId', 'appId']
        missing_fields = [field for field in required_fields if not firebase_config.get(field)]
        
        if missing_fields:
            current_app.logger.error(f"Missing Firebase config fields: {missing_fields}")
            return jsonify({"error": "Firebase configuration incomplete on server."}), 500
            
        return jsonify({
            "success": True,
            "config": firebase_config
        })
        
    except Exception as e:
        current_app.logger.error(f"Error providing Firebase config: {e}")
        return jsonify({"error": "Failed to retrieve Firebase configuration."}), 500

@api_bp.route('/ephemeral-token', methods=['POST'])
@login_required  
def get_ephemeral_token():
    """Generates an ephemeral token for the user."""
    user_id = g.user_id
    if not user_id:
        return jsonify({"error": "User not authenticated"}), 401

    try:
        # Generate a new ephemeral token
        from concierge.utils.firestore_client import create_ephemeral_token
        token = create_ephemeral_token(user_id)

        if token:
            return jsonify({
                "success": True,
                "token": token
            })
        else:
            return jsonify({"error": "Failed to generate ephemeral token"}), 500

    except Exception as e:
        print(f"Error generating ephemeral token: {e}")
        traceback.print_exc()
        return jsonify({"error": f"Server error: {e}"}), 500

# === Current API Routes ===

@api_bp.route('/generate-qna', methods=['POST'])
@login_required
def generate_qna_route():
    """Generates Q&A pairs from provided text using Gemini."""
    if not genai_enabled:
        return jsonify({"error": "Q&A generation feature is disabled."}), 503

    data = request.get_json()
    knowledge_base_text = data.get('text')

    if not knowledge_base_text:
         return jsonify({"error": "No text provided for Q&A generation."}), 400

    try:
        qna_list = generate_qna(knowledge_base_text) # Use the helper
        return jsonify({"success": True, "qna": qna_list})
    except Exception as e:
        print(f"Error in /generate-qna route: {e}")
        traceback.print_exc()
        return jsonify({"success": False, "error": f"Failed to generate Q&A: {e}"}), 500

@api_bp.route('/user-property')
@login_required
def get_user_property():
    """Gets the first property associated with the logged-in user."""
    user_id = g.user_id
    # No need to check for database availability since we're using Firestore
    try:
        # First try to get properties from Firestore
        properties = list_properties_by_host(user_id)

        if properties:
            print(f"Found property for user {user_id} in Firestore: {properties[0].get('id')}") # DEBUG
            return jsonify({"success": True, "property": properties[0]})
        else:
            print(f"No properties found for user {user_id} in Firestore, trying DynamoDB") # DEBUG

            # If no properties found in Firestore, try DynamoDB as fallback
            from concierge.utils.dynamodb_client import list_properties_by_host as dynamo_list_properties
            dynamo_properties = dynamo_list_properties(user_id)

            if dynamo_properties:
                print(f"Found property for user {user_id} in DynamoDB: {dynamo_properties[0].get('PK')}") # DEBUG
                return jsonify({"success": True, "property": dynamo_properties[0]})
            else:
                print(f"No properties found for user {user_id} in either database") # DEBUG
                # It's not necessarily an error if a user has no properties yet
                return jsonify({"success": True, "property": None, "message": "No property found for this user."})
    except Exception as e:
        print(f"Error fetching property for user {user_id}: {e}")
        traceback.print_exc()
        return jsonify({"success": False, "error": f"Server error fetching property: {e}"}), 500

@api_bp.route('/user-properties')
@login_required
def get_user_properties():
    """Gets all properties associated with the logged-in user."""
    user_id = g.user_id
    print(f"Fetching properties for host dashboard, user: {user_id}")
    # No need to check for database availability since we're using Firestore
    try:
        # First try to get properties from Firestore
        raw_properties = list_properties_by_host(user_id)

        # Process properties to make them easier to work with in the frontend
        processed_properties = []
        for prop in raw_properties:
            # Firestore already includes 'id' field
            property_id = prop.get('id', '')
            processed_prop = {
                'id': property_id,
                'name': prop.get('name', 'Unnamed Property'),
                'description': prop.get('description', 'No description'),
                'address': prop.get('address', 'N/A'),
                'icalUrl': prop.get('icalUrl', ''),
                # Include any other needed fields here
            }
            processed_properties.append(processed_prop)

        print(f"Found {len(processed_properties)} properties for user {user_id} in Firestore") # DEBUG

        # If no properties found in Firestore, try DynamoDB as fallback
        if not processed_properties:
            from concierge.utils.dynamodb_client import list_properties_by_host as dynamo_list_properties
            print(f"No properties found in Firestore, trying DynamoDB for user {user_id}")
            dynamo_properties = dynamo_list_properties(user_id)

            for prop in dynamo_properties:
                # Extract property ID from PK (format: PROPERTY#id)
                pk = prop.get('PK', '')
                property_id = pk.replace('PROPERTY#', '') if pk.startswith('PROPERTY#') else pk

                processed_prop = {
                    'id': property_id,
                    'name': prop.get('Name', 'Unnamed Property'),
                    'description': prop.get('Description', 'No description'),
                    'address': prop.get('Address', 'N/A'),
                    'icalUrl': prop.get('ICalUrl', ''),
                }
                processed_properties.append(processed_prop)

            print(f"Found {len(dynamo_properties)} properties for user {user_id} in DynamoDB") # DEBUG

        return jsonify({"success": True, "properties": processed_properties})
    except Exception as e:
        print(f"Error fetching properties for user {user_id}: {e}")
        traceback.print_exc()
        return jsonify({"success": False, "error": f"Server error fetching properties: {e}"}), 500

@api_bp.route('/property/<property_id>', methods=['PUT'])
@login_required
def update_property(property_id):
    """Updates details for a specific property."""
    user_id = g.user_id
    # No need to check for database availability since we're using Firestore

    data = request.get_json()
    if not data:
        return jsonify({"error": "No update data provided"}), 400

    # Get the property from Firestore to check ownership
    property_item = get_property(property_id)

    try:
        if not property_item:
            return jsonify({"error": "Property not found"}), 404

        # Check if the user owns this property
        if property_item.get('hostId') != user_id:
            return jsonify({"error": "Unauthorized - you do not own this property"}), 403

        # Update the property using Firestore update_property function
        success = update_property(property_id, data)

        if success:
            return jsonify({"success": True, "message": "Property updated successfully"})
        else:
            return jsonify({"success": False, "error": "Failed to update property"}), 500

    except Exception as e:
        print(f"Error updating property {property_id}: {e}")
        traceback.print_exc()
        return jsonify({"success": False, "error": f"Server error updating property: {e}"}), 500

@api_bp.route('/property/<property_id>', methods=['DELETE'])
@login_required
def delete_property_api(property_id):
    """Deletes a specific property."""
    user_id = g.user_id
    # No need to check for database availability since we're using Firestore

    try:
        # Get the property from Firestore to check ownership
        property_item = get_property(property_id)

        if not property_item:
            return jsonify({"error": "Property not found"}), 404

        # Check if the user owns this property
        if property_item.get('hostId') != user_id:
            return jsonify({"error": "Unauthorized - you do not own this property"}), 403

        # Delete the property using Firestore delete_property function
        success = delete_property(property_id)

        if success:
            return jsonify({"success": True, "message": "Property deleted successfully"})
        else:
            return jsonify({"success": False, "error": "Failed to delete property"}), 500

    except Exception as e:
        print(f"Error deleting property {property_id}: {e}")
        return jsonify({"success": False, "error": f"Server error deleting property: {e}"}), 500

@api_bp.route('/property/<property_id>/knowledge-base', methods=['POST'])
@login_required
def upload_knowledge_base(property_id):
    """Handles file upload for a property's knowledge base."""
    user_id = g.user_id
    # No need to check for database availability since we're using Firestore

    if 'knowledgeFile' not in request.files:
        return jsonify({"error": "No file part in the request"}), 400

    file = request.files['knowledgeFile']
    if file.filename == '':
        return jsonify({"error": "No selected file"}), 400

    # Import the functions we need
    from werkzeug.utils import secure_filename

    # 1. Security Check: Verify property ownership
    try:
        property_item = get_property(property_id)
        if not property_item:
            return jsonify({"error": "Property not found"}), 404

        if property_item.get('hostId') != user_id:
            print(f"Security Alert: User {user_id} attempted upload for property {property_id} owned by {property_item.get('hostId')}")
            return jsonify({"error": "Permission denied"}), 403
    except Exception as e:
        print(f"Error verifying property ownership for {property_id}: {e}")
        return jsonify({"error": f"Server error checking property: {e}"}), 500

    # 2. Save File Temporarily
    upload_folder = current_app.config.get('UPLOAD_FOLDER', '/tmp/concierge_uploads')
    if not os.path.exists(upload_folder):
        os.makedirs(upload_folder)

    filename = secure_filename(f"{property_id}_{file.filename}")
    file_path = os.path.join(upload_folder, filename)

    try:
        file.save(file_path)
        print(f"File temporarily saved to {file_path}")

        # 3. Extract Text (Using helper)
        knowledge_base_text = extract_text_from_file(file_path)
        if knowledge_base_text is None:
             # Clean up saved file before returning error
             os.remove(file_path)
             return jsonify({"error": "Unsupported file type or error during text extraction."}), 400

        # 4. Prepare Payload for Lambda
        payload = {
            "property_id": property_id,
            "user_id": user_id,
            "text_content": knowledge_base_text,
        }

        # 5. Update Property in Firestore
        update_property(property_id, {'knowledgeStatus': 'processing'})

        return jsonify({"success": True, "message": "Knowledge base received and processing started."})

    except Exception as e:
        print(f"Error processing knowledge base upload for property {property_id}: {e}")
        traceback.print_exc()
        # Clean up if file was saved
        if os.path.exists(file_path):
             try:
                  os.remove(file_path)
                  print(f"Cleaned up temporary file {file_path} after error.")
             except OSError as remove_err:
                  print(f"Error removing temporary file {file_path} after error: {remove_err}")
        return jsonify({"error": f"Server error during upload processing: {e}"}), 500


@api_bp.route('/property/<property_id>/reservations')
@login_required
def get_reservations(property_id):
    """Gets reservations for a specific property."""
    user_id = g.user_id
    # No need to check for database availability since we're using Firestore

    try:
        # Get property to verify ownership
        property_item = get_property(property_id)
        if not property_item:
            return jsonify({"error": "Property not found"}), 404

        # Check if the user owns this property
        if property_item.get('HostId') != user_id:
            print(f"Security Alert: User {user_id} attempted GET reservations for property {property_id} owned by {property_item.get('HostId')}")
            return jsonify({"error": "Permission denied"}), 403

        # Fetch reservations linked to this property
        reservations = list_property_reservations(property_id)

        print(f"Found {len(reservations)} reservations for property {property_id}") # DEBUG
        return jsonify({"success": True, "reservations": reservations})

    except Exception as e:
        print(f"Error fetching reservations for property {property_id}: {e}")
        traceback.print_exc()
        return jsonify({"error": f"Server error fetching reservations: {e}"}), 500

@api_bp.route('/knowledge/<item_id>/update', methods=['PUT'])
@login_required
def update_knowledge_item_route(item_id):
    """Update a knowledge item using Firestore with the new schema."""
    user_id = g.user_id
    # No need to check for database availability since we're using Firestore

    # Get the JSON data
    data = request.get_json()
    if not data:
        return jsonify({"success": False, "error": "No data provided"}), 400

    try:
        # Extract fields from request
        item_type = data.get('type')
        tags = data.get('tags', [])
        content = data.get('content')

        # Validate required fields
        if not content:
            return jsonify({"success": False, "error": "Content is required"}), 400

        if not item_type:
            return jsonify({"success": False, "error": "Type is required"}), 400

        # Get the knowledge item
        item_data = get_knowledge_item(item_id)
        if not item_data:
            return jsonify({"success": False, "error": "Knowledge item not found"}), 404

        # Get property ID from the item
        property_id = item_data.get('propertyId')
        if not property_id:
            return jsonify({"success": False, "error": "Invalid knowledge item (missing property ID)"}), 400

        # Get property data to check ownership
        property_data = get_property(property_id)
        if not property_data:
            return jsonify({"success": False, "error": "Property not found"}), 404

        # Check if the user is authorized to update this item
        if property_data.get('hostId') != user_id:
            print(f"Unauthorized attempt to update knowledge item {item_id} by user {user_id}")
            return jsonify({"success": False, "error": "You don't have permission to update this item"}), 403

        # Prepare the update data
        update_data = {
            'type': item_type,
            'tags': tags,
            'content': content,
            'updatedAt': datetime.now(timezone.utc)
        }

        # If status is provided and valid, update it
        status = data.get('status')
        valid_statuses = ['pending_review', 'approved', 'rejected']
        if status and status in valid_statuses:
            update_data['status'] = status

        # Update the item
        success = update_knowledge_item(item_id, update_data)
        if success:
            print(f"Knowledge item {item_id} updated by user {user_id}")

            # Generate new embedding if content changed
            if content != item_data.get('content'):
                try:
                    embedding = generate_embedding(content)
                    if embedding:
                        update_knowledge_item(item_id, {'embedding': embedding})
                        print(f"Generated new embedding for knowledge item {item_id}")
                except Exception as embed_err:
                    print(f"Warning: Failed to generate embedding for item {item_id}: {embed_err}")
                    # Continue even if embedding generation fails

            return jsonify({"success": True, "message": "Knowledge item updated successfully"})
        else:
            return jsonify({"success": False, "error": "Failed to update knowledge item"}), 500

    except Exception as e:
        print(f"Error updating knowledge item {item_id}: {e}")
        traceback.print_exc()
        return jsonify({"success": False, "error": f"An error occurred: {str(e)}"}), 500

@api_bp.route('/knowledge/<item_id>/delete', methods=['DELETE'])
@login_required
def delete_knowledge_item_route(item_id):
    """Delete a knowledge item."""
    user_id = g.user_id
    # No need to check for database availability since we're using Firestore

    try:
        # Get the knowledge item
        item_data = get_knowledge_item(item_id)
        if not item_data:
            return jsonify({"success": False, "error": "Knowledge item not found"}), 404

        # Get property ID from the item
        property_id = item_data.get('propertyId')
        if not property_id:
            return jsonify({"success": False, "error": "Invalid knowledge item (missing property ID)"}), 400

        # Get property data to check ownership
        property_data = get_property(property_id)
        if not property_data:
            return jsonify({"success": False, "error": "Property not found"}), 404

        # Check if the user is authorized to delete this item
        if property_data.get('hostId') != user_id:
            print(f"Unauthorized attempt to delete knowledge item {item_id} by user {user_id}")
            return jsonify({"success": False, "error": "You don't have permission to delete this item"}), 403

        # Delete the item
        success = delete_knowledge_item(item_id)
        if success:
            print(f"Knowledge item {item_id} deleted by user {user_id}")
            return jsonify({"success": True, "message": "Knowledge item deleted successfully"})
        else:
            return jsonify({"success": False, "error": "Failed to delete knowledge item"}), 500

    except Exception as e:
        print(f"Error deleting knowledge item {item_id}: {e}")
        traceback.print_exc()
        return jsonify({"success": False, "error": f"An error occurred: {str(e)}"}), 500

@api_bp.route('/knowledge/<item_id>/generate-embedding', methods=['POST'])
@login_required
def generate_item_embedding(item_id):
    """Generate or regenerate embedding for a knowledge item."""
    user_id = g.user_id
    # No need to check for database availability since we're using Firestore

    try:
        # Get the knowledge item
        item_data = get_knowledge_item(item_id)
        if not item_data:
            return jsonify({"success": False, "error": "Knowledge item not found"}), 404

        # Get property ID from the item
        property_id = item_data.get('propertyId')
        if not property_id:
            return jsonify({"success": False, "error": "Invalid knowledge item (missing property ID)"}), 400

        # Get property data to check ownership
        property_data = get_property(property_id)
        if not property_data:
            return jsonify({"success": False, "error": "Property not found"}), 404

        # Check if the user is authorized to update this item
        if property_data.get('hostId') != user_id:
            print(f"Unauthorized attempt to update knowledge item {item_id} by user {user_id}")
            return jsonify({"success": False, "error": "You don't have permission to update this item"}), 403

        # Get content from the item
        content = item_data.get('content')
        if not content:
            return jsonify({"success": False, "error": "Item has no content to generate embedding from"}), 400

        # Generate embedding
        embedding = generate_embedding(content)
        if not embedding:
            return jsonify({"success": False, "error": "Failed to generate embedding"}), 500

        # Update the item with the new embedding
        update_data = {
            'embedding': embedding,
            'updatedAt': datetime.now(timezone.utc)
        }

        success = update_knowledge_item(item_id, update_data)
        if success:
            print(f"Generated embedding for knowledge item {item_id}")
            return jsonify({"success": True, "message": "Embedding generated successfully"})
        else:
            return jsonify({"success": False, "error": "Failed to update knowledge item with new embedding"}), 500

    except Exception as e:
        print(f"Error generating embedding for knowledge item {item_id}: {e}")
        traceback.print_exc()
        return jsonify({"success": False, "error": f"An error occurred: {str(e)}"}), 500

@api_bp.route('/properties/<property_id>/knowledge/generate-all-embeddings', methods=['POST'])
@login_required
def generate_all_embeddings(property_id):
    """
    Trigger embedding generation for all knowledge items for a property that:
    1. Are missing embeddings
    2. Have outdated content (content changed but embedding not updated)

    Uses batch processing to improve performance.
    """
    print(f"Embedding generation request for all knowledge items of property {property_id}")

    # Get user ID from session
    user_id = g.user_id
    if not user_id:
        return jsonify({'error': 'Authentication required'}), 401

    # Define batch size for processing
    BATCH_SIZE = 5  # Process 5 items per batch

    try:
        # Get the property
        property_data = get_property(property_id)

        if not property_data:
            return jsonify({'error': 'Property not found'}), 404

        if property_data.get('hostId') != user_id:
            return jsonify({'error': 'You do not have permission to manage this property'}), 403

        # Get all knowledge items for this property
        all_items = list_knowledge_items_by_property(property_id)

        if not all_items:
            return jsonify({
                'success': True,
                'message': 'No knowledge items found for this property',
                'total_items': 0,
                'missing_embeddings': 0,
                'outdated_embeddings': 0
            })

        print(f"Found {len(all_items)} knowledge items for property {property_id}")

        # Identify items that need embedding generation
        items_needing_embedding = []
        missing_embeddings = 0
        outdated_embeddings = 0

        for item in all_items:
            if not item.get('embedding'):
                items_needing_embedding.append(item['id'])
                missing_embeddings += 1
            elif item.get('contentUpdatedAt') and item.get('embeddingUpdatedAt'):
                # Check if content was updated after embedding was generated
                content_updated = item['contentUpdatedAt']
                embedding_updated = item['embeddingUpdatedAt']
                if content_updated > embedding_updated:
                    items_needing_embedding.append(item['id'])
                    outdated_embeddings += 1

        if not items_needing_embedding:
            return jsonify({
                'success': True,
                'message': 'All knowledge items already have up-to-date embeddings',
                'total_items': len(all_items),
                'missing_embeddings': 0,
                'outdated_embeddings': 0
            })

        # Process items in batches
        batch_count = (len(items_needing_embedding) + BATCH_SIZE - 1) // BATCH_SIZE  # Ceiling division

        # Start processing in the background (this would typically be done by a worker)
        # For now, we'll just return success and let the client poll for status
        return jsonify({
            'success': True,
            'message': f'Embedding generation started for {len(items_needing_embedding)} items',
            'total_items': len(all_items),
            'missing_embeddings': missing_embeddings,
            'outdated_embeddings': outdated_embeddings,
            'batch_size': BATCH_SIZE,
            'batch_count': batch_count
        })

    except Exception as e:
        print(f"Error during embedding generation: {e}")
        traceback.print_exc()
        return jsonify({'error': f'Failed to process embedding generation: {str(e)}'}), 500

@api_bp.route('/property/<property_id>/reservations/<reservation_id>/phone', methods=['PUT'])
@login_required
def update_reservation_phone(property_id, reservation_id):
    """Updates the guest phone number for a specific reservation."""
    user_id = g.user_id # Host's user ID from session
    # No need to check for database availability since we're using Firestore

    data = request.get_json()
    phone_number = data.get('phone_number')

    if not phone_number: # Add validation for phone format if needed
        return jsonify({"error": "Phone number is required."}), 400

    try:
        # Verify the property exists and user is the owner
        property_item = get_property(property_id)
        if not property_item:
            return jsonify({"error": "Property not found"}), 404

        # Check if the user owns this property
        if property_item.get('hostId') != user_id:
            print(f"Security Alert: User {user_id} attempted to modify reservation for property {property_id} owned by {property_item.get('hostId')}")
            return jsonify({"error": "Permission denied"}), 403

        # Update the reservation with the new phone number
        success = update_reservation_phone(reservation_id, phone_number)

        if success:
            print(f"User {user_id} updated phone for reservation {reservation_id} to {phone_number}")
            return jsonify({"success": True, "message": "Phone number updated successfully"})
        else:
            return jsonify({"error": "Failed to update phone number"}), 500

    except Exception as e:
        print(f"Error updating phone for reservation {reservation_id}: {e}")
        traceback.print_exc()
        return jsonify({"error": f"Server error during update: {e}"}), 500

@api_bp.route('/property/<property_id>/reservations/<reservation_id>/contacts', methods=['PUT'])
@login_required
def update_reservation_contacts_api(property_id, reservation_id):
    """Updates the additional contacts for a specific reservation."""
    user_id = g.user_id # Host's user ID from session
    # No need to check for database availability since we're using Firestore

    # Import the update_reservation_contacts function
    from concierge.utils.firestore_client import update_reservation_contacts

    data = request.get_json()
    contacts = data.get('contacts')

    if contacts is None:
        return jsonify({"error": "Contacts data is required."}), 400

    try:
        # Verify the property exists and user is the owner
        property_item = get_property(property_id)
        if not property_item:
            return jsonify({"error": "Property not found"}), 404

        # Check if the user owns this property
        if property_item.get('hostId') != user_id:
            print(f"Security Alert: User {user_id} attempted to modify reservation contacts for property {property_id} owned by {property_item.get('hostId')}")
            return jsonify({"error": "Permission denied"}), 403

        # Validate contacts format
        if not isinstance(contacts, list):
            return jsonify({"error": "Contacts must be an array"}), 400

        for contact in contacts:
            if not isinstance(contact, dict) or 'name' not in contact or 'phone' not in contact:
                return jsonify({"error": "Each contact must have name and phone"}), 400

        # Update the reservation with the new contacts
        success = update_reservation_contacts(reservation_id, contacts)

        if success:
            print(f"User {user_id} updated contacts for reservation {reservation_id}")
            return jsonify({"success": True, "message": "Contacts updated successfully"})
        else:
            return jsonify({"error": "Failed to update contacts"}), 500

    except Exception as e:
        print(f"Error updating contacts for reservation {reservation_id}: {e}")
        traceback.print_exc()
        return jsonify({"error": f"Server error during update: {e}"}), 500

@api_bp.route('/users/by-phone/<phone_number>', methods=['GET'])
@login_required
def get_user_by_phone(phone_number):
    """Gets user data by phone number."""
    current_user_id = g.user_id

    try:
        # Import functions from firestore_client
        from concierge.utils.firestore_client import find_user_by_phone

        # Find user by phone number
        user_data = find_user_by_phone(phone_number)

        if user_data:
            # For security, only return limited user data
            safe_user_data = {
                'displayName': user_data.get('displayName') or user_data.get('name') or 'Guest',
                'phoneNumber': phone_number,
                'id': user_data.get('id') or user_data.get('uid')
            }
            return jsonify({"success": True, "user": safe_user_data})
        else:
            # Try to find a reservation with this phone number to get the guest name
            from concierge.utils.firestore_client import find_reservation_by_phone

            reservation = find_reservation_by_phone(phone_number)
            if reservation:
                guest_name = reservation.get('guestName') or 'Guest'
                return jsonify({
                    "success": True,
                    "user": {
                        'displayName': guest_name,
                        'phoneNumber': phone_number,
                        'id': None
                    }
                })

            return jsonify({"success": False, "error": "User not found"}), 404
    except Exception as e:
        print(f"Error finding user by phone {phone_number}: {e}")
        traceback.print_exc()
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/reservations', methods=['GET'])
@login_required
def get_reservations_by_phone():
    """Gets all reservations for a phone number (query parameter)."""
    current_user_id = g.user_id

    # Get phone number from query parameter
    phone_number = request.args.get('phone')
    if not phone_number:
        return jsonify({"error": "Phone number is required"}), 400

    # Import functions from firestore_client
    from concierge.utils.firestore_client import list_reservations_by_phone, get_firestore_db

    try:
        # Check if Firestore is properly initialized
        db = get_firestore_db()
        if not db:
            print(f"Error: Firestore database not initialized properly")
            return jsonify({
                "success": False,
                "error": "Database connection error. Please refresh the page and try again.",
                "firebase_error": True
            }), 500

        print(f"Fetching reservations for phone number: {phone_number}")

        # Get all reservations for this phone number
        reservations_list = list_reservations_by_phone(phone_number)
        print(f"Found {len(reservations_list)} reservations for phone: {phone_number}")

        # Return the reservations
        return jsonify({
            "success": True,
            "reservations": reservations_list,
            "count": len(reservations_list)
        })

    except Exception as e:
        print(f"Error fetching reservations for phone {phone_number}: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({
            "success": False,
            "error": f"Server error: {e}"
        }), 500

@api_bp.route('/reservations/<user_id>', methods=['GET'])
def get_user_reservations(user_id):
    """Gets all reservations for a user by their phone number (supports magic link sessions)."""
    from flask import session, request
    
    # Check authentication - support both regular login and magic link sessions
    regular_user_id = session.get('user_id')
    magic_link_session = request.cookies.get('magicLinkSession')
    authenticated_user_id = None

    # Regular authentication
    if regular_user_id:
        authenticated_user_id = regular_user_id
    # Magic link authentication
    elif magic_link_session:
        try:
            from concierge.utils.session_manager import validate_session
            is_valid, temp_user_id, reason = validate_session(magic_link_session)
            if is_valid and temp_user_id:
                authenticated_user_id = temp_user_id
                current_app.logger.info(f"Magic link user {temp_user_id} accessing reservations")
            else:
                current_app.logger.error(f"Invalid magic link session for reservations: {reason}")
                return jsonify({"error": "Authentication required"}), 401
        except Exception as e:
            current_app.logger.error(f"Error validating magic link session: {e}")
            return jsonify({"error": "Session validation error"}), 401
    else:
        return jsonify({"error": "Authentication required"}), 401

    # Allow users to view only their own reservations (security check)
    if user_id != authenticated_user_id:
        print(f"Security Alert: User {authenticated_user_id} attempted to access reservations for user {user_id}")
        return jsonify({"error": "Permission denied"}), 403

    # Import functions from firestore_client
    from concierge.utils.firestore_client import get_user, list_reservations_by_phone, get_firestore_db

    try:
        # Check if Firestore is properly initialized
        db = get_firestore_db()
        if not db:
            print(f"Error: Firestore database not initialized properly")
            return jsonify({
                "success": False,
                "error": "Database connection error. Please refresh the page and try again.",
                "firebase_error": True
            }), 500

        # Get user info from Firestore
        user_data = get_user(user_id)
        if not user_data:
            print(f"User {user_id} not found in Firestore")

            # Attempt to get the user from Firebase Auth as fallback
            try:
                print(f"Attempting to get user from Firebase Auth: {user_id}")
                from firebase_admin import auth
                auth_user = auth.get_user(user_id)
                if auth_user:
                    print(f"Found user in Firebase Auth: {auth_user.uid}, Phone: {auth_user.phone_number}")
                    user_data = {
                        'uid': auth_user.uid,
                        'phoneNumber': auth_user.phone_number,
                        'displayName': auth_user.display_name
                    }
            except Exception as auth_error:
                print(f"Error getting user from Firebase Auth: {auth_error}")
                # Continue with the flow even if this fails

        # If we still don't have user data
        if not user_data:
            print(f"No user data found in Firestore or Auth for {user_id}")
            # Check if we have a phone number in the request or session to use as fallback
            fallback_phone = request.args.get('phone') or session.get('phone_number')
            if fallback_phone:
                print(f"Using fallback phone number from request/session: {fallback_phone}")
                user_data = {'phoneNumber': fallback_phone}
            else:
                # Create minimal user data to continue
                user_data = {'uid': user_id}

        # Get phone number from user data - handle both camelCase and snake_case
        user_phone_number = user_data.get('phoneNumber') or user_data.get('phone_number')
        print(f"Fetching reservations for phone number: {user_phone_number}")

        # Get all reservations for this user by phone number
        reservations_list = []
        if user_phone_number:
            # Get reservations by exact phone number match
            reservations_list = list_reservations_by_phone(user_phone_number)
            print(f"Found {len(reservations_list)} reservations for exact phone match: {user_phone_number}")

            # If no reservations found, try matching by last 4 digits
            if not reservations_list and len(user_phone_number) >= 4:
                # Get the last 4 digits of the phone number
                last_four_digits = user_phone_number[-4:]
                print(f"No reservations found for exact match, trying last 4 digits: {last_four_digits}")

                # Get all reservations from Firestore
                from concierge.utils.firestore_client import get_firestore_db
                db = get_firestore_db()
                if db:
                    # Query all reservations
                    all_reservations_query = db.collection('reservations').stream()
                    all_reservations = [doc.to_dict() for doc in all_reservations_query]
                    print(f"Retrieved {len(all_reservations)} total reservations from Firestore")

                    # Examine a sample reservation to debug
                    if all_reservations and len(all_reservations) > 0:
                        sample = all_reservations[0]
                        print(f"Sample reservation structure: {sample.keys()}")

                        # Check for phone fields in the sample
                        for field in ['guestPhoneNumber', 'GuestPhoneNumber', 'guest_phone_number', 'guestPhone']:
                            if field in sample:
                                print(f"Found phone field in sample: {field} = {sample[field]}")

                        # Check for additional contacts
                        for field in ['additionalContacts', 'AdditionalContacts', 'additional_contacts']:
                            if field in sample and sample[field]:
                                print(f"Found additional contacts in sample: {field} = {sample[field]}")

                    # Filter reservations by last 4 digits of phone number
                    for res in all_reservations:
                        # Add document ID to the reservation data
                        res_id = res.get('id')
                        if not res_id:
                            # If the reservation doesn't have an ID field, try to get it from the document reference
                            for doc in all_reservations_query:
                                if doc.to_dict() == res:
                                    res['id'] = doc.id
                                    break

                        # Check primary guest phone number
                        guest_phone = res.get('guestPhoneNumber')
                        if guest_phone and guest_phone.endswith(last_four_digits):
                            reservations_list.append(res)
                            print(f"Found reservation with last 4 digits match in primary phone: {guest_phone}")
                            continue

                        # Check guestPhoneLast4 field specifically
                        guest_phone_last4 = res.get('guestPhoneLast4')
                        if guest_phone_last4 == last_four_digits:
                            reservations_list.append(res)
                            print(f"Found reservation with matching guestPhoneLast4: {guest_phone_last4}")
                            continue

                        # Check additional contacts
                        additional_contacts = res.get('additional_contacts', [])
                        for contact in additional_contacts:
                            contact_phone = contact.get('phone') or contact.get('phoneNumber') or contact.get('phone_number')
                            if contact_phone and contact_phone.endswith(last_four_digits):
                                reservations_list.append(res)
                                print(f"Found reservation with last 4 digits match in additional contact: {contact_phone}")
                                break

                    print(f"Found {len(reservations_list)} reservations for last 4 digits match: {last_four_digits}")

        # If we still don't have reservations, try a direct Firestore query by user ID
        if not reservations_list:
            print(f"No reservations found by phone, trying direct query by user ID: {user_id}")
            try:
                user_reservations_query = db.collection('reservations').where('userId', '==', user_id)
                user_reservations = [doc.to_dict() for doc in user_reservations_query.stream()]

                # Add doc ID if missing
                for res in user_reservations:
                    if 'id' not in res:
                        for doc in user_reservations_query.stream():
                            if doc.to_dict() == res:
                                res['id'] = doc.id
                                break

                # Add to our list
                reservations_list.extend(user_reservations)
                print(f"Found {len(user_reservations)} reservations by direct user ID query")

                # Try another query format
                guest_id_query = db.collection('reservations').where('guestId', '==', user_id)
                guest_id_reservations = [doc.to_dict() for doc in guest_id_query.stream()]

                # Add doc ID if missing
                for res in guest_id_reservations:
                    if 'id' not in res and not any(r.get('id') == res.get('id') for r in reservations_list):
                        for doc in guest_id_query.stream():
                            if doc.to_dict() == res:
                                res['id'] = doc.id
                                break

                # Add to our list
                for res in guest_id_reservations:
                    if not any(r.get('id') == res.get('id') for r in reservations_list):
                        reservations_list.append(res)

                print(f"Found {len(guest_id_reservations)} additional reservations by guest ID query")
            except Exception as query_error:
                print(f"Error during direct user ID query: {query_error}")

        # If we still don't have any reservations and just need to show something for testing
        if not reservations_list and 'DEBUG_MODE' in os.environ:
            print("Creating test reservation for debugging")
            
            # Import datetime for test reservation creation
            from datetime import datetime, timezone, timedelta
            
            # Create a basic test reservation with a real property ID
            # Make sure not to use 'test-property-123' as the property ID
            test_reservation = {
                'id': f"test-{user_id[:8]}",
                'propertyId': '1a344329-2670-4b34-a4f6-e28513a3200c',  # Use a real property ID from logs
                'startDate': (datetime.now(timezone.utc) - timedelta(days=1)).isoformat(),
                'endDate': (datetime.now(timezone.utc) + timedelta(days=5)).isoformat(),
                'guestName': user_data.get('displayName') or 'Test Guest',
                'guestPhoneNumber': user_phone_number or 'Unknown',
                'propertyName': 'Chicago Place',
                'propertyAddress': '2425 W Lyndale St, Chicago, IL 60647',
                'additionalContacts': []
            }

            # Only add the test reservation if the property exists in Firestore
            property_data = get_property(test_reservation['propertyId'])
            if property_data:
                reservations_list.append(test_reservation)
                print("Added test reservation for debugging with verified property ID")
            else:
                print("Not adding test reservation because property ID does not exist in Firestore")

        # Sort reservations by date - active first, then upcoming, then past
        from datetime import datetime, timezone
        now = datetime.now(timezone.utc)

        def get_reservation_order(res):
            # Support both field naming conventions (camelCase and snake_case)
            start = res.get('startDate') or res.get('checkInDate') or res.get('check_in_date')
            end = res.get('endDate') or res.get('checkOutDate') or res.get('check_out_date')

            # Convert ISO strings to datetime for comparison if needed
            if isinstance(start, str):
                try:
                    start = datetime.fromisoformat(start.replace('Z', '+00:00'))
                except (ValueError, TypeError):
                    start = now  # Fallback

            if isinstance(end, str):
                try:
                    end = datetime.fromisoformat(end.replace('Z', '+00:00'))
                except (ValueError, TypeError):
                    end = now  # Fallback

            # Check for None values before comparison
            if start is None or end is None:
                return 3  # Missing dates - lowest priority

            # Determine status for sorting
            if start <= now <= end:
                return 0  # Active - highest priority
            elif start > now:
                return 1  # Upcoming - second priority
            else:
                return 2  # Past - lowest priority

        # Sort reservations using a try-except block to handle potential errors
        try:
            sorted_reservations = sorted(reservations_list, key=get_reservation_order)
        except Exception as sort_error:
            print(f"Error sorting reservations: {sort_error}")
            # Return unsorted reservations if sorting fails
            sorted_reservations = reservations_list

        # Standardize field names for frontend
        for res in sorted_reservations:
            # Ensure all reservations have these fields with consistent naming
            # If the ID is not in the data, use the document ID
            if 'id' not in res:
                print(f"Warning: Reservation missing 'id' field: {res}")

            # Extract property ID if needed
            property_id = res.get('propertyId')
            if not property_id and res.get('PK', '').startswith('PROPERTY#'):
                property_id = res.get('PK', '').replace('PROPERTY#', '')
                res['propertyId'] = property_id

            # Import date utilities for consistent date formatting
            from concierge.utils.date_utils import ensure_date_only_format

            # Standardize field names and ensure dates are in date-only format
            res['id'] = res.get('id', '')
            res['propertyId'] = property_id or res.get('propertyId', '')

            # Normalize dates to ensure consistent formatting across all clients
            start_date_raw = res.get('startDate') or res.get('StartDate') or res.get('checkInDate') or res.get('CheckInDate')
            end_date_raw = res.get('endDate') or res.get('EndDate') or res.get('checkOutDate') or res.get('CheckOutDate')

            res['startDate'] = ensure_date_only_format(start_date_raw) or ''
            res['endDate'] = ensure_date_only_format(end_date_raw) or ''

            res['guestName'] = res.get('guestName') or res.get('GuestName') or ''
            res['guestPhoneNumber'] = res.get('guestPhoneNumber') or res.get('GuestPhoneNumber') or res.get('guest_phone') or ''
            res['additionalContacts'] = res.get('additional_contacts') or res.get('additionalContacts') or res.get('AdditionalContacts') or []

            # Make sure propertyName and propertyAddress are set
            if not res.get('propertyName') and property_id:
                # Use the property ID as a fallback name
                res['propertyName'] = f"Beach House {property_id[-4:]}"

            # Add address if missing
            if not res.get('propertyAddress') and property_id:
                # Use a fallback address
                res['propertyAddress'] = "123 Beach Avenue"

            # These are now standardized field names in the response
            # Even if the database has different casing or field names
            # The frontend will look for these specific fields

        print(f"Returning {len(sorted_reservations)} standardized reservations")
        return jsonify({
            "success": True,
            "reservations": sorted_reservations
        })

    except Exception as e:
        print(f"Error fetching reservations for user {user_id}: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/properties/<property_id>/knowledge/reingest-all', methods=['POST'])
@login_required
def reingest_all_knowledge(property_id):
    """
    Legacy endpoint for re-ingestion - no longer needed with Firestore migration.
    Knowledge items are now processed directly in Firestore with vector embeddings.
    """
    print(f"Legacy re-ingestion request for property {property_id} - no longer needed with Firestore")

    # Get user ID from session
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'error': 'Authentication required'}), 401

    return jsonify({
        'success': True,
        'message': 'Re-ingestion no longer needed - knowledge items are processed directly in Firestore with vector embeddings',
        'migration_note': 'This endpoint is deprecated. Knowledge items are automatically processed when created/updated in Firestore.'
    })

@api_bp.route('/knowledge-items', methods=['GET'])
def get_knowledge_items():
    """
    Get knowledge items for a property.

    Query parameters:
    - propertyId: ID of the property to get knowledge items for
    - status: (optional) Filter by status (e.g., 'approved')
    """
    property_id = request.args.get('propertyId')
    status = request.args.get('status')

    if not property_id:
        return jsonify({"error": "Property ID is required"}), 400

    try:
        # Get knowledge items from Firestore
        if status:
            items = list_knowledge_items_by_property(property_id, status)
        else:
            items = list_knowledge_items_by_property(property_id)

        # Filter out items without embeddings if needed for RAG
        items_with_embeddings = [item for item in items if item.get('embedding')]

        # Log the results
        print(f"Found {len(items)} knowledge items for property {property_id}")
        print(f"{len(items_with_embeddings)} items have embeddings")

        # Return the items
        return jsonify({
            "success": True,
            "items": items,
            "total": len(items),
            "with_embeddings": len(items_with_embeddings)
        })

    except Exception as e:
        print(f"Error getting knowledge items: {e}")
        traceback.print_exc()
        return jsonify({"error": f"Failed to get knowledge items: {str(e)}"}), 500

@api_bp.route('/chat/query', methods=['POST'])
def process_chat_query():
    """
    Process a chat query using RAG with Firestore.

    Request body:
    - query: The user's query
    - propertyId: ID of the property to search in
    - conversationHistory: (optional) Previous messages in the conversation
    """
    data = request.get_json()
    if not data:
        return jsonify({"error": "No data provided"}), 400

    query = data.get('query')
    property_id = data.get('propertyId')
    conversation_history = data.get('conversationHistory', [])

    if not query:
        return jsonify({"error": "Query is required"}), 400

    if not property_id:
        return jsonify({"error": "Property ID is required"}), 400

    try:
        # Get property context
        property_data = get_property(property_id)
        if not property_data:
            return jsonify({"error": "Property not found"}), 404

        # Process the query with RAG
        result = process_query_with_rag(
            user_query=query,
            property_id=property_id,
            property_context=property_data,
            conversation_history=conversation_history
        )

        # Return the response
        return jsonify({
            "success": True,
            "response": result.get('response', ''),
            "has_context": result.get('has_context', False),
            "context_used": result.get('context_used', [])
        })

    except Exception as e:
        print(f"Error processing chat query: {e}")
        traceback.print_exc()
        return jsonify({"error": f"Failed to process query: {str(e)}"}), 500

@login_required
def get_knowledge_item_status(item_id):
    """
    Get the current status of a knowledge item.
    Used to check if ingestion has completed successfully.
    """
    # Get user ID from session
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'error': 'Authentication required'}), 401

    # Import the DynamoDB functions we need
    from concierge.utils.dynamodb_client import get_knowledge_item, get_property

    try:
        # Get the knowledge item
        item_data = get_knowledge_item(item_id)

        if not item_data:
            return jsonify({'error': 'Knowledge item not found'}), 404

        property_id = item_data.get('PropertyId')

        if not property_id:
            # Check if property_id is in the PK field
            pk = item_data.get('PK', '')
            if pk and pk.startswith('PROPERTY#'):
                property_id = pk.replace('PROPERTY#', '')
            else:
                return jsonify({'error': 'Invalid knowledge item (missing property ID)'}), 400

        # Check if user has access to this property
        property_data = get_property(property_id)

        if not property_data:
            return jsonify({'error': 'Property not found'}), 404

        if property_data.get('hostId') != user_id:
            return jsonify({'error': 'You do not have permission to access this knowledge item'}), 403

        # Return the current status and other relevant info
        status = item_data.get('Status', 'unknown')
        error_message = item_data.get('ErrorMessage', None)

        return jsonify({
            'success': True,
            'status': status,
            'error': error_message,
            'last_updated': item_data.get('UpdatedAt', None),
            'migration_note': 'LanceDB status no longer tracked - using Firestore with vector embeddings'
        })

    except Exception as e:
        print(f"Error getting knowledge item status: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': f'Failed to get knowledge item status: {str(e)}'}), 500

@api_bp.route('/property/<property_id>')
def get_property_by_id(property_id):
    """Gets property details by ID (accessible to both hosts and guests)."""
    try:
        # Special handling for test property IDs
        if property_id == 'test-property-123':
            print(f"Returning mock data for test property ID: {property_id}")

            # Mock property data for testing
            mock_property = {
                "success": True,
                "property": {
                    "id": property_id,
                    "propertyId": property_id,
                    "name": "Beach House -123 (Test Property)",
                    "address": "123 Beach Avenue",
                    "description": "This is a test property for development purposes.",
                    "wifiNetwork": "TestWiFi",
                    "wifiPassword": "password123",
                    "checkInTime": "3:00 PM",
                    "checkOutTime": "11:00 AM",
                    "hostName": "Test Host",
                    "rules": "No pets. No smoking. Quiet hours from 10pm to 8am.",
                    "hostId": "test-host-123"
                }
            }

            return jsonify(mock_property)

        # Get property details from Firestore
        property_data = get_property(property_id)
        print(f"Property data from Firestore for {property_id}: {property_data}")

        if not property_data:
            # Try to find property in DynamoDB as fallback
            try:
                from concierge.utils.dynamodb_client import get_property as get_dynamo_property
                property_data = get_dynamo_property(property_id)
                print(f"Property data from DynamoDB for {property_id}: {property_data}")

                if not property_data:
                    return jsonify({"success": False, "error": "Property not found"}), 404
            except Exception as dynamo_error:
                print(f"Error fetching property from DynamoDB: {dynamo_error}")
                return jsonify({"success": False, "error": "Property not found"}), 404

        # Extract WiFi details if available
        wifi_network = ""
        wifi_password = ""

        # Check for wifiDetails field
        if 'wifiDetails' in property_data:
            wifi = property_data.get('wifiDetails', {})
            if isinstance(wifi, dict):
                wifi_network = wifi.get('network', '')
                wifi_password = wifi.get('password', '')

        # Check for direct WiFi fields (support both camelCase and PascalCase)
        if 'wifiNetwork' in property_data:
            wifi_network = property_data.get('wifiNetwork', '')
        elif 'WifiNetwork' in property_data:
            wifi_network = property_data.get('WifiNetwork', '')

        if 'wifiPassword' in property_data:
            wifi_password = property_data.get('wifiPassword', '')
        elif 'WifiPassword' in property_data:
            wifi_password = property_data.get('WifiPassword', '')

        # Get host name from host ID if available (support both camelCase and PascalCase)
        host_name = property_data.get('hostName', property_data.get('HostName', 'Your Host'))
        host_id = property_data.get('hostId', property_data.get('HostId', ''))

        if host_id:
            try:
                # Get the host's user profile
                host_data = get_user(host_id)
                if host_data:
                    # Try different possible field names for the host's name
                    host_name = host_data.get('name',
                                host_data.get('displayName',
                                host_data.get('fullName', 'Your Host')))
                    print(f"Retrieved host name from user profile: {host_name}")
            except Exception as user_err:
                print(f"Error retrieving host name for host ID {host_id}: {user_err}")
                # Fall back to generic name or existing hostName
                host_name = property_data.get('hostName', property_data.get('HostName', 'Your Host'))

        # Return property details with WiFi information and host name
        response_data = {
            "success": True,
            "property": {
                "id": property_id,
                "propertyId": property_id,  # Add this for consistency
                "name": property_data.get('name', property_data.get('Name', 'Unknown Property')),
                "address": property_data.get('address', property_data.get('Address', 'No address available')),
                "description": property_data.get('description', property_data.get('Description', '')),
                "wifiNetwork": wifi_network,
                "wifiPassword": wifi_password,
                "checkInTime": property_data.get('checkInTime', property_data.get('CheckInTime', '')),
                "checkOutTime": property_data.get('checkOutTime', property_data.get('CheckOutTime', '')),
                "hostName": host_name,
                "rules": property_data.get('rules', property_data.get('Rules', '')),
                "hostId": host_id  # Include the host ID for reference
            }
        }

        print(f"Returning property details for {property_id}: {response_data}")
        return jsonify(response_data)
    except Exception as e:
        print(f"Error fetching property {property_id}: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({"success": False, "error": f"Server error: {str(e)}"}), 500

@api_bp.route('/user/role', methods=['PUT'])
@login_required
def update_user_role():
    """Updates the role of the currently logged-in user."""
    user_id = g.user_id
    if not user_id:
        return jsonify({"error": "User not authenticated"}), 401

    data = request.get_json()
    if not data or 'role' not in data:
        return jsonify({"error": "Role is required"}), 400

    new_role = data['role']
    if new_role not in ['guest', 'host', 'property_manager']:
        return jsonify({"error": "Invalid role. Must be 'guest', 'host', or 'property_manager'"}), 400

    try:
        # Update the user's role in Firestore
        success = update_user(user_id, {'role': new_role})

        if success:
            # Update the role in the session
            session['user_role'] = new_role
            g.user_role = new_role
            return jsonify({"success": True, "message": "Role updated successfully", "role": new_role})
        else:
            return jsonify({"error": "Failed to update role"}), 500

    except Exception as e:
        print(f"Error updating role for user {user_id}: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({"error": f"Server error updating role: {e}"}), 500

@api_bp.route('/user/profile', methods=['GET'])
@login_required
def get_user_profile():
    """Gets the profile of the currently logged-in user."""
    user_id = g.user_id
    if not user_id:
        return jsonify({"error": "User not authenticated"}), 401

    try:
        # Get user data from Firestore
        user_data = get_user(user_id)

        if user_data:
            # Return only safe profile data
            profile_data = {
                'displayName': user_data.get('displayName') or user_data.get('DisplayName') or '',
                'email': user_data.get('email') or user_data.get('Email') or '',
                'phoneNumber': user_data.get('phoneNumber') or user_data.get('PhoneNumber') or '',
                'role': user_data.get('role', 'guest')
            }

            return jsonify({
                "success": True,
                "user": profile_data
            })
        else:
            return jsonify({"error": "User profile not found"}), 404

    except Exception as e:
        print(f"Error fetching profile for user {user_id}: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({"error": f"Server error fetching profile: {e}"}), 500

@api_bp.route('/change-pin', methods=['POST'])
@login_required
def change_pin():
    """Change user's PIN without requiring current PIN."""
    user_id = g.user_id
    if not user_id:
        return jsonify({"error": "User not authenticated"}), 401

    data = request.get_json()
    if not data:
        return jsonify({"error": "No data provided"}), 400

    new_pin = data.get('newPin')

    # Validation
    if not new_pin or len(str(new_pin)) != 4:
        return jsonify({"error": "New PIN must be exactly 4 digits"}), 400

    try:
        # Validate PIN contains only digits
        if not str(new_pin).isdigit():
            return jsonify({"error": "PIN must contain only numbers"}), 400

        # Update the user's PIN in Firestore
        from concierge.utils.firestore_client import update_user
        
        success = update_user(user_id, {"customPin": new_pin})

        if success:
            return jsonify({
                "success": True,
                "message": "PIN changed successfully"
            })
        else:
            return jsonify({"error": "Failed to update PIN"}), 500

    except Exception as e:
        print(f"Error changing PIN for user {user_id}: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({"error": f"Server error changing PIN: {e}"}), 500

@api_bp.route('/conversation-history/<user_id>/<property_id>', methods=['GET'])
def get_conversation_history(user_id, property_id):
    """Get recent conversation history for a user and property within the last 24 hours."""
    try:
        from concierge.utils.dynamodb_client import list_user_conversations, get_conversation

        # Get recent conversations (last 24 hours)
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=24)
        conversations = list_user_conversations(user_id, limit=50)

        recent_messages = []

        for conversation in conversations:
            # Check if conversation is recent and for the correct property
            if (conversation.get('PropertyId') == property_id and
                conversation.get('LastUpdateTime')):

                # Parse the timestamp
                try:
                    last_update = datetime.fromisoformat(conversation['LastUpdateTime'].replace('Z', '+00:00'))
                    if last_update >= cutoff_time:
                        # Get full conversation details
                        full_conversation = get_conversation(conversation['ConversationId'], property_id)
                        if full_conversation and full_conversation.get('Messages'):
                            # Extract messages that are within the time window
                            for message in full_conversation['Messages']:
                                try:
                                    msg_time = datetime.fromisoformat(message['timestamp'].replace('Z', '+00:00'))
                                    if msg_time >= cutoff_time:
                                        recent_messages.append({
                                            'role': message['role'],
                                            'text': message['text'],
                                            'timestamp': message['timestamp'],
                                            'conversation_id': conversation['ConversationId'],
                                            'channel': conversation.get('Channel', 'text_chat')
                                        })
                                except (ValueError, KeyError):
                                    continue
                except (ValueError, KeyError):
                    continue

        # Sort messages by timestamp
        recent_messages.sort(key=lambda x: x['timestamp'])

        return jsonify({
            'success': True,
            'messages': recent_messages,
            'count': len(recent_messages)
        })

    except Exception as e:
        current_app.logger.error(f"Error retrieving conversation history for user {user_id}, property {property_id}: {e}")
        return jsonify({
            'success': False,
            'error': 'Failed to retrieve conversation history',
            'messages': []
        }), 500

# === Conversation Management Endpoints ===

@api_bp.route('/conversations/create', methods=['POST'])
@login_required
def create_conversation_session():
    """Create a new conversation session for voice calls or text chat."""
    try:
        data = request.get_json()
        property_id = data.get('property_id')
        user_id = data.get('user_id') or g.user_id
        guest_name = data.get('guest_name')
        phone_number = data.get('phone_number')
        channel = data.get('channel', 'text_chat')  # Default to text_chat
        reservation_id = data.get('reservation_id')

        if not property_id:
            return jsonify({"error": "Property ID is required"}), 400

        # Import DynamoDB conversation functions
        from concierge.utils.dynamodb_client import create_conversation_session

        conversation_id = create_conversation_session(
            property_id=property_id,
            user_id=user_id,
            guest_name=guest_name,
            reservation_id=reservation_id,
            phone_number=phone_number,
            channel=channel
        )

        if conversation_id:
            return jsonify({
                "success": True,
                "conversation_id": conversation_id
            })
        else:
            return jsonify({"error": "Failed to create conversation session"}), 500

    except Exception as e:
        print(f"Error creating conversation session: {e}")
        traceback.print_exc()
        return jsonify({"error": f"Server error: {e}"}), 500

@api_bp.route('/conversations/message', methods=['POST'])
@login_required
def add_conversation_message():
    """Add a message to an existing conversation."""
    try:
        data = request.get_json()
        conversation_id = data.get('conversation_id')
        property_id = data.get('property_id')
        message_data = data.get('message')

        if not conversation_id or not property_id or not message_data:
            return jsonify({"error": "Conversation ID, property ID, and message data are required"}), 400

        # Import DynamoDB conversation functions
        from concierge.utils.dynamodb_client import add_message_to_conversation

        success = add_message_to_conversation(
            conversation_id=conversation_id,
            property_id=property_id,
            message_data=message_data
        )

        if success:
            return jsonify({"success": True})
        else:
            return jsonify({"error": "Failed to add message to conversation"}), 500

    except Exception as e:
        print(f"Error adding message to conversation: {e}")
        traceback.print_exc()
        return jsonify({"error": f"Server error: {e}"}), 500

@api_bp.route('/conversations/<conversation_id>', methods=['GET'])
@login_required
def get_conversation(conversation_id):
    """Get conversation details and messages."""
    try:
        property_id = request.args.get('property_id')

        if not property_id:
            return jsonify({"error": "Property ID is required"}), 400

        # Import DynamoDB conversation functions
        from concierge.utils.dynamodb_client import get_conversation

        conversation = get_conversation(conversation_id, property_id)

        if conversation:
            return jsonify({
                "success": True,
                "conversation": conversation
            })
        else:
            return jsonify({"error": "Conversation not found"}), 404

    except Exception as e:
        print(f"Error getting conversation: {e}")
        traceback.print_exc()
        return jsonify({"error": f"Server error: {e}"}), 500

# === End Conversation Management Endpoints ===


# === Magic Link Management Endpoints ===

@api_bp.route('/property/<property_id>/reservations/<reservation_id>/magic-link', methods=['POST'])
@login_required
def generate_magic_link_for_reservation(property_id, reservation_id):
    """Generate a new magic link for a reservation."""
    try:
        from datetime import datetime, timezone, timedelta

        # Verify user has access to this property
        user_id = g.user_id
        property_data = get_property(property_id)
        if not property_data or property_data.get('hostId') != user_id:
            return jsonify({"error": "Property not found or access denied."}), 404

        # Get reservation to verify it exists and belongs to this property
        reservation = get_reservation(reservation_id)
        if not reservation or reservation.get('propertyId') != property_id:
            return jsonify({"error": "Reservation not found or doesn't belong to this property."}), 404

        # Calculate expiration time (checkout date + 24 hours)
        checkout_date = reservation.get('endDate')
        if checkout_date:
            if isinstance(checkout_date, str):
                checkout_datetime = datetime.fromisoformat(checkout_date.replace('Z', '+00:00'))
            else:
                checkout_datetime = checkout_date
            expires_at = checkout_datetime + timedelta(hours=24)
        else:
            # Default to 7 days from now if no checkout date
            expires_at = datetime.now(timezone.utc) + timedelta(days=7)

        # Get current request's base URL for proper domain/port sync
        base_url = request.url_root.rstrip('/')
        
        # Create magic link with current base URL
        raw_token = create_magic_link(reservation_id, expires_at, base_url)
        if not raw_token:
            return jsonify({"error": "Failed to create magic link."}), 500

        # Generate full URL with current base URL
        magic_link_url = generate_magic_link_url(raw_token, base_url)

        return jsonify({
            "success": True,
            "magic_link_url": magic_link_url,
            "expires_at": expires_at.isoformat()
        })

    except Exception as e:
        current_app.logger.error(f"Error generating magic link: {e}")
        return jsonify({"error": "An error occurred while generating the magic link."}), 500

@api_bp.route('/property/<property_id>/reservations/<reservation_id>/magic-links', methods=['GET'])
@login_required
def list_magic_links_for_reservation(property_id, reservation_id):
    """List all magic links for a reservation."""
    try:
        # Verify user has access to this property
        user_id = g.user_id
        property_data = get_property(property_id)
        if not property_data or property_data.get('hostId') != user_id:
            return jsonify({"error": "Property not found or access denied."}), 404

        # Get magic links for this reservation
        magic_links = list_magic_links_by_reservation(reservation_id)

        # URLs are now stored in the magic link documents
        # No need to modify the URL field as it's already included

        return jsonify({
            "success": True,
            "magic_links": magic_links
        })

    except Exception as e:
        current_app.logger.error(f"Error listing magic links: {e}")
        return jsonify({"error": "An error occurred while listing magic links."}), 500

@api_bp.route('/property/<property_id>/reservations/<reservation_id>/magic-links/<link_id>/revoke', methods=['POST'])
@login_required
def revoke_magic_link_for_reservation(property_id, reservation_id, link_id):
    """Revoke a magic link."""
    try:
        # Verify user has access to this property
        user_id = g.user_id
        property_data = get_property(property_id)
        if not property_data or property_data.get('hostId') != user_id:
            return jsonify({"error": "Property not found or access denied."}), 404

        # Revoke the magic link using the link_id (which is the token_hash)
        from concierge.utils.firestore_client import update_magic_link
        success = update_magic_link(link_id, {
            'is_active': False,
            'status': 'revoked'
        })

        if success:
            return jsonify({"success": True})
        else:
            return jsonify({"error": "Failed to revoke magic link."}), 500

    except Exception as e:
        current_app.logger.error(f"Error revoking magic link: {e}")
        return jsonify({"error": "An error occurred while revoking the magic link."}), 500

# === System Cleanup Endpoints ===

@api_bp.route('/system/cleanup', methods=['POST'])
@login_required
def manual_system_cleanup():
    """Manually trigger system cleanup tasks."""
    try:
        # Check if user has admin privileges (you might want to add role checking here)
        user_id = g.user_id
        
        # Import cleanup functions
        from concierge.utils.firestore_client import perform_daily_cleanup
        
        # Perform cleanup
        cleanup_results = perform_daily_cleanup()
        
        return jsonify({
            "success": True,
            "message": "System cleanup completed successfully",
            "results": cleanup_results
        })

    except Exception as e:
        current_app.logger.error(f"Error during manual system cleanup: {e}")
        return jsonify({"error": "An error occurred during system cleanup."}), 500

@api_bp.route('/system/cleanup/status', methods=['GET'])
@login_required
def get_cleanup_status():
    """Get status of items that would be cleaned up."""
    try:
        from concierge.utils.firestore_client import initialize_firebase, get_firestore_client
        from datetime import datetime, timezone
        
        if not initialize_firebase():
            return jsonify({"error": "Database initialization failed"}), 500
        
        db = get_firestore_client()
        now = datetime.now(timezone.utc)
        
        # Count expired magic links
        expired_magic_links_query = db.collection('magic_links').where('expires_at', '<', now).where('is_active', '==', True)
        expired_magic_links_count = len(list(expired_magic_links_query.stream()))
        
        # Count expired temporary users
        expired_temp_users_query = db.collection('users').where('isTemporary', '==', True).where('expiresAt', '<', now)
        expired_temp_users_count = len(list(expired_temp_users_query.stream()))
        
        return jsonify({
            "success": True,
            "status": {
                "expired_magic_links": expired_magic_links_count,
                "expired_temp_users": expired_temp_users_count,
                "total_items_to_cleanup": expired_magic_links_count + expired_temp_users_count
            }
        })

    except Exception as e:
        current_app.logger.error(f"Error getting cleanup status: {e}")
        return jsonify({"error": "An error occurred while getting cleanup status."}), 500

# === End Magic Link Management Endpoints ===