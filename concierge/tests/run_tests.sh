#!/bin/bash

# Authentication System Test Runner
# Runs comprehensive tests following AUTH_TESTING_PLAN.md

echo "🔐 AUTHENTICATION SYSTEM TEST RUNNER"
echo "====================================="

# Check if we're in the right directory
if [ ! -f "test_auth_basic.py" ]; then
    echo "❌ Error: Please run this script from the tests directory"
    echo "   cd concierge/tests && ./run_tests.sh"
    exit 1
fi

# Function to run tests with timing
run_test_suite() {
    local test_file=$1
    local test_name=$2
    
    echo ""
    echo "🧪 Running $test_name..."
    echo "----------------------------------------"
    
    start_time=$(date +%s)
    
    if python3 "$test_file"; then
        end_time=$(date +%s)
        duration=$((end_time - start_time))
        echo "✅ $test_name completed successfully in ${duration}s"
        return 0
    else
        end_time=$(date +%s)
        duration=$((end_time - start_time))
        echo "❌ $test_name failed after ${duration}s"
        return 1
    fi
}

# Initialize counters
total_suites=0
passed_suites=0

echo "Starting test execution at $(date)"
echo ""

# Run basic tests
total_suites=$((total_suites + 1))
if run_test_suite "test_auth_basic.py" "Basic Authentication Tests"; then
    passed_suites=$((passed_suites + 1))
fi

# Run comprehensive test runner (if available)
if [ -f "run_auth_tests.py" ]; then
    total_suites=$((total_suites + 1))
    echo ""
    echo "🧪 Running Comprehensive Test Suite..."
    echo "----------------------------------------"
    
    start_time=$(date +%s)
    
    if python3 run_auth_tests.py; then
        end_time=$(date +%s)
        duration=$((end_time - start_time))
        echo "✅ Comprehensive tests completed successfully in ${duration}s"
        passed_suites=$((passed_suites + 1))
    else
        end_time=$(date +%s)
        duration=$((end_time - start_time))
        echo "❌ Comprehensive tests failed after ${duration}s"
    fi
fi

# Print summary
echo ""
echo "🏁 TEST EXECUTION SUMMARY"
echo "========================"
echo "📊 Test suites run: $total_suites"
echo "✅ Test suites passed: $passed_suites"
echo "❌ Test suites failed: $((total_suites - passed_suites))"

if [ $passed_suites -eq $total_suites ]; then
    echo "🎉 All test suites passed!"
    echo "🚀 Authentication system is ready for deployment"
    exit 0
else
    echo "⚠️  Some test suites failed"
    echo "🔧 Please review and fix failing tests before deployment"
    exit 1
fi 