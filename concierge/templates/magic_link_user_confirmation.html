<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Confirmation - Guestrix</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <style>
        .confirmation-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .confirmation-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            max-width: 500px;
            width: 100%;
            margin: 1rem;
        }
        .confirmation-icon {
            font-size: 3rem;
            color: #667eea;
            text-align: center;
            margin-bottom: 1rem;
        }
        .btn-confirm {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 0.75rem 2rem;
            font-weight: 600;
            margin: 0.5rem;
        }
        .btn-deny {
            background: #6c757d;
            border: none;
            padding: 0.75rem 2rem;
            font-weight: 600;
            margin: 0.5rem;
        }
        .user-name {
            color: #667eea;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="confirmation-container">
        <div class="confirmation-card">
            <div class="confirmation-icon">
                <i class="fas fa-user-check"></i>
            </div>
            
            <h2 class="text-center mb-4">Account Confirmation</h2>
            
            <div class="text-center mb-4">
                <p class="lead">{{ message }}</p>
                <p class="text-muted">
                    If you are <span class="user-name">{{ user_name }}</span>, we'll log you into your permanent account. 
                    If not, we'll create a new temporary account for you.
                </p>
            </div>

            <div class="d-flex justify-content-center flex-wrap">
                <form method="POST" action="{{ url_for('magic.confirm_migrated_user', token=token) }}" class="d-inline">
                    <input type="hidden" name="confirmation" value="yes">
                    <button type="submit" class="btn btn-primary btn-confirm">
                        <i class="fas fa-check me-2"></i>Yes, that's me
                    </button>
                </form>
                
                <form method="POST" action="{{ url_for('magic.confirm_migrated_user', token=token) }}" class="d-inline">
                    <input type="hidden" name="confirmation" value="no">
                    <button type="submit" class="btn btn-secondary btn-deny">
                        <i class="fas fa-times me-2"></i>No, I'm someone else
                    </button>
                </form>
            </div>

            <div class="text-center mt-4">
                <small class="text-muted">
                    Having trouble? <a href="mailto:<EMAIL>">Contact support</a>
                </small>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Font Awesome -->
    <script src="https://kit.fontawesome.com/your-font-awesome-kit.js"></script>
</body>
</html> 