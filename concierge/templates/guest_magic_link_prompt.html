{% extends "base.html" %}

{% block title %}Guest Account - Concierge{% endblock %}

{% block head %}
<style>
    .auth-container {
        min-height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 1rem;
    }
    
    .auth-card {
        background: white;
        border-radius: 1rem;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        padding: 2rem;
        width: 100%;
        max-width: 450px;
    }
    
    .auth-title {
        color: #4a5568;
        font-size: 1.875rem;
        font-weight: bold;
        text-align: center;
        margin-bottom: 0.5rem;
    }
    
    .auth-subtitle {
        color: #718096;
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .phone-display {
        background: #f7fafc;
        border: 1px solid #e2e8f0;
        border-radius: 0.5rem;
        padding: 0.75rem;
        text-align: center;
        margin-bottom: 2rem;
        color: #4a5568;
        font-weight: 600;
    }
    
    .info-card {
        background: #fffaf0;
        border: 2px solid #fed7aa;
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .info-card .icon {
        font-size: 3rem;
        margin-bottom: 1rem;
    }
    
    .info-card .title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #c05621;
        margin-bottom: 1rem;
    }
    
    .info-card .message {
        color: #9c4221;
        line-height: 1.5;
        margin-bottom: 1.5rem;
    }
    
    .steps-list {
        background: #f7fafc;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1.5rem;
    }
    
    .steps-list .step {
        display: flex;
        align-items: flex-start;
        margin-bottom: 0.75rem;
        color: #4a5568;
        font-size: 0.875rem;
    }
    
    .steps-list .step:last-child {
        margin-bottom: 0;
    }
    
    .steps-list .step-number {
        background: #667eea;
        color: white;
        width: 1.5rem;
        height: 1.5rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 0.75rem;
        margin-right: 0.75rem;
        flex-shrink: 0;
    }
    
    .btn-primary {
        width: 100%;
        background: #667eea;
        color: white;
        border: none;
        padding: 0.875rem 1rem;
        border-radius: 0.5rem;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: background-color 0.2s;
        margin-bottom: 1rem;
        text-decoration: none;
        display: inline-block;
        text-align: center;
    }
    
    .btn-primary:hover {
        background: #5a67d8;
        text-decoration: none;
        color: white;
    }
    
    .btn-secondary {
        width: 100%;
        background: transparent;
        color: #667eea;
        border: 2px solid #667eea;
        padding: 0.875rem 1rem;
        border-radius: 0.5rem;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s;
        text-decoration: none;
        display: inline-block;
        text-align: center;
    }
    
    .btn-secondary:hover {
        background: #667eea;
        color: white;
        text-decoration: none;
    }
    
    .btn-link {
        background: none;
        border: none;
        color: #667eea;
        text-decoration: underline;
        cursor: pointer;
        font-size: 0.875rem;
        padding: 0.5rem;
    }
    
    .btn-link:hover {
        color: #5a67d8;
    }
    
    .logo {
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .logo h1 {
        color: #667eea;
        font-size: 2rem;
        font-weight: bold;
        margin: 0;
    }
    
    .alternative-section {
        text-align: center;
        padding-top: 1.5rem;
        border-top: 1px solid #e2e8f0;
        margin-top: 1.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="auth-container">
    <div class="auth-card">
        <div class="logo">
            <h1>🏠 Concierge</h1>
        </div>
        
        <h2 class="auth-title">Guest Account Setup</h2>
        <p class="auth-subtitle">Almost there! Let's get you connected properly</p>
        
        <div class="phone-display">
            📱 {{ phone_number | default('****-****') }}
        </div>
        
        <div class="info-card">
            <div class="icon">🔗</div>
            <div class="title">Use Your Host's Magic Link</div>
            <div class="message">
                As a guest, you should have received a special magic link from your host. 
                This link provides instant access to your reservation and property information.
            </div>
            
            <div class="steps-list">
                <div class="step">
                    <div class="step-number">1</div>
                    <div>Check your email or text messages from your host</div>
                </div>
                <div class="step">
                    <div class="step-number">2</div>
                    <div>Look for a message with a link to access your reservation</div>
                </div>
                <div class="step">
                    <div class="step-number">3</div>
                    <div>Click the link to get instant access to everything you need</div>
                </div>
            </div>
        </div>
        
        <a href="mailto:?subject=Requesting%20Property%20Access%20Link" class="btn-primary">
            📧 Contact Your Host for the Link
        </a>
        
        <div class="alternative-section">
            <p style="color: #718096; font-size: 0.875rem; margin-bottom: 1rem;">
                Don't have a host or want to create a standalone guest account anyway?
            </p>
            
            <form method="POST" action="{{ url_for('auth.signup_choice') }}" style="display: inline;">
                <input type="hidden" name="account_type" value="guest">
                <button type="submit" class="btn-link">
                    Create standalone guest account
                </button>
            </form>
        </div>
        
        <div style="text-align: center; margin-top: 1rem;">
            <a href="{{ url_for('auth.phone_login') }}" class="btn-secondary">
                ← Use Different Phone Number
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Auto-focus the contact host button for better UX
document.addEventListener('DOMContentLoaded', function() {
    const contactBtn = document.querySelector('.btn-primary');
    if (contactBtn) {
        contactBtn.focus();
    }
});
</script>
{% endblock %} 