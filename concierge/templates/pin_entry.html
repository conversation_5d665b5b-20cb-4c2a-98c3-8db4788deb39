{% extends "base.html" %}

{% block title %}Enter PIN - Guestrix{% endblock %}

{% block head %}
<style>
    .auth-container {
        min-height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 1rem;
    }
    
    .auth-card {
        background: white;
        border-radius: 1rem;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        padding: 2rem;
        width: 100%;
        max-width: 400px;
    }
    
    .auth-title {
        color: #4a5568;
        font-size: 1.875rem;
        font-weight: bold;
        text-align: center;
        margin-bottom: 0.5rem;
    }
    
    .auth-subtitle {
        color: #718096;
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .phone-display {
        background: #f7fafc;
        border: 1px solid #e2e8f0;
        border-radius: 0.5rem;
        padding: 0.75rem;
        text-align: center;
        margin-bottom: 1.5rem;
        color: #4a5568;
        font-weight: 600;
    }
    
    .pin-input-container {
        display: flex;
        justify-content: center;
        gap: 0.5rem;
        margin-bottom: 1.5rem;
    }
    
    .pin-digit {
        width: 3rem;
        height: 3rem;
        border: 2px solid #e2e8f0;
        border-radius: 0.5rem;
        text-align: center;
        font-size: 1.5rem;
        font-weight: bold;
        color: #4a5568;
    }
    
    .pin-digit:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
    
    .hidden-input {
        position: absolute;
        left: -9999px;
        opacity: 0;
    }
    
    .btn-primary {
        width: 100%;
        background: #667eea;
        color: white;
        border: none;
        padding: 0.875rem 1rem;
        border-radius: 0.5rem;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: background-color 0.2s;
        margin-bottom: 1rem;
    }
    
    .btn-primary:hover {
        background: #5a67d8;
    }
    
    .btn-secondary {
        width: 100%;
        background: transparent;
        color: #667eea;
        border: 2px solid #667eea;
        padding: 0.875rem 1rem;
        border-radius: 0.5rem;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s;
        text-decoration: none;
        display: inline-block;
        text-align: center;
    }
    
    .btn-secondary:hover {
        background: #667eea;
        color: white;
        text-decoration: none;
    }
    
    .btn-link {
        background: none;
        border: none;
        color: #667eea;
        text-decoration: underline;
        cursor: pointer;
        font-size: 0.875rem;
        padding: 0.5rem;
    }
    
    .btn-link:hover {
        color: #5a67d8;
    }
    
    .alert {
        padding: 0.875rem;
        border-radius: 0.5rem;
        margin-bottom: 1rem;
    }
    
    .alert-error {
        background: #fed7d7;
        border: 1px solid #feb2b2;
        color: #c53030;
    }
    
    .alert-warning {
        background: #fefcbf;
        border: 1px solid #faf089;
        color: #975a16;
    }
    
    .attempts-indicator {
        text-align: center;
        margin-bottom: 1rem;
    }
    
    .attempts-dot {
        display: inline-block;
        width: 0.5rem;
        height: 0.5rem;
        border-radius: 50%;
        margin: 0 0.25rem;
        background: #e2e8f0;
    }
    
    .attempts-dot.used {
        background: #f56565;
    }
    
    .logo {
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .logo h1 {
        color: #667eea;
        font-size: 2rem;
        font-weight: bold;
        margin: 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="auth-container">
    <div class="auth-card">
        <div class="logo">
            <h1>🏠 Guestrix</h1>
        </div>
        
        <h2 class="auth-title">Enter Your PIN</h2>
        <p class="auth-subtitle">Enter your 4-digit PIN to continue</p>
        
        <div class="phone-display">
            📱 {{ phone_number | default('****-****') }}
        </div>
        
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }}">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <!-- Attempt Indicator -->
        {% if attempts > 0 %}
            <div class="attempts-indicator">
                <div style="font-size: 0.875rem; color: #718096; margin-bottom: 0.5rem;">
                    Failed attempts:
                </div>
                {% for i in range(3) %}
                    <span class="attempts-dot {{ 'used' if i < attempts else '' }}"></span>
                {% endfor %}
            </div>
        {% endif %}
        
        <form method="POST" id="pin-form">
            <!-- Hidden input for form submission -->
            <input type="text" name="pin" id="pin-hidden" class="hidden-input" maxlength="4">
            
            <!-- PIN display -->
            <div class="pin-input-container">
                <input type="text" class="pin-digit" maxlength="1" data-index="0" autocomplete="off">
                <input type="text" class="pin-digit" maxlength="1" data-index="1" autocomplete="off">
                <input type="text" class="pin-digit" maxlength="1" data-index="2" autocomplete="off">
                <input type="text" class="pin-digit" maxlength="1" data-index="3" autocomplete="off">
            </div>
            
            <button type="submit" class="btn-primary" id="submit-btn" disabled>
                Verify PIN
            </button>
        </form>
        
        <div style="text-align: center; margin-bottom: 1rem;">
            <a href="{{ url_for('auth.phone_login') }}" class="btn-secondary">
                ← Use Different Phone Number
            </a>
        </div>
        
        <div style="text-align: center; border-top: 1px solid #e2e8f0; padding-top: 1rem;">
            <p style="color: #718096; font-size: 0.875rem; margin-bottom: 0.5rem;">
                Forgot your PIN?
            </p>
            <button type="button" class="btn-link" onclick="recoverWithOTP()">
                Verify with text message instead
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const pinInputs = document.querySelectorAll('.pin-digit');
    const hiddenInput = document.getElementById('pin-hidden');
    const submitBtn = document.getElementById('submit-btn');
    
    // Focus first input
    pinInputs[0].focus();
    
    pinInputs.forEach((input, index) => {
        input.addEventListener('input', function(e) {
            const value = e.target.value;
            
            // Only allow digits
            if (!/^\d*$/.test(value)) {
                e.target.value = '';
                return;
            }
            
            // Update hidden input
            updateHiddenInput();
            
            // Move to next input if value entered
            if (value && index < pinInputs.length - 1) {
                pinInputs[index + 1].focus();
            }
            
            // Enable submit button if all digits entered
            updateSubmitButton();
        });
        
        input.addEventListener('keydown', function(e) {
            // Move to previous input on backspace
            if (e.key === 'Backspace' && !e.target.value && index > 0) {
                pinInputs[index - 1].focus();
            }
            
            // Submit on Enter if PIN is complete
            if (e.key === 'Enter' && getCurrentPin().length === 4) {
                document.getElementById('pin-form').submit();
            }
        });
        
        input.addEventListener('paste', function(e) {
            e.preventDefault();
            const paste = e.clipboardData.getData('text');
            const digits = paste.replace(/\D/g, '').slice(0, 4);
            
            // Fill in the digits
            for (let i = 0; i < digits.length && i < pinInputs.length; i++) {
                pinInputs[i].value = digits[i];
            }
            
            updateHiddenInput();
            updateSubmitButton();
            
            // Focus next empty input or last input
            const nextEmpty = Array.from(pinInputs).findIndex(input => !input.value);
            if (nextEmpty !== -1) {
                pinInputs[nextEmpty].focus();
            } else {
                pinInputs[pinInputs.length - 1].focus();
            }
        });
    });
    
    function getCurrentPin() {
        return Array.from(pinInputs).map(input => input.value).join('');
    }
    
    function updateHiddenInput() {
        hiddenInput.value = getCurrentPin();
    }
    
    function updateSubmitButton() {
        const pin = getCurrentPin();
        submitBtn.disabled = pin.length !== 4;
    }
});

function recoverWithOTP() {
    // Redirect to OTP recovery
    window.location.href = '{{ url_for("auth.otp_recovery") }}';
}
</script>
{% endblock %} 