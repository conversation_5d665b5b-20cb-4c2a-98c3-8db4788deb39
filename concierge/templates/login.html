{% extends "base.html" %}

{% block title %}Guestrix - Login{% endblock %}

{% block head %}
<style>
    #recaptcha-container {
        margin-top: 20px;
    }
    .auth-step {
        display: none;
    }
    #phone-step {
        display: block;
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-6 offset-md-3">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h3 class="mb-0">Login to Guestrix</h3>
            </div>
            <div class="card-body">
                <div id="phone-step" class="auth-step">
                    <p class="mb-3">Enter your phone number to receive a verification code:</p>
                    <div class="form-group mb-3">
                        <label for="phone-number">Phone Number (with country code)</label>
                        <input type="tel" id="phone-number" class="form-control" placeholder="****** 123 4567">
                    </div>
                    <button id="send-code-button" class="btn btn-primary">Send Verification Code</button>
                    <div id="recaptcha-container"></div>
                </div>

                <div id="code-step" class="auth-step">
                    <p class="mb-3">Enter the verification code sent to your phone:</p>
                    <div class="form-group mb-3">
                        <label for="verification-code">Verification Code</label>
                        <input type="text" id="verification-code" class="form-control" placeholder="123456">
                    </div>
                    <button id="verify-code-button" class="btn btn-primary">Verify Code</button>
                    <button id="back-to-phone" class="btn btn-link">Back</button>
                </div>

                <div id="success-step" class="auth-step">
                    <div class="text-center">
                        <div class="alert alert-success">
                            <i class="bi bi-check-circle-fill"></i> Successfully authenticated!
                        </div>
                        <p>Redirecting to your dashboard...</p>
                    </div>
                </div>

                <div id="error-message" class="alert alert-danger mt-3" style="display: none;"></div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Flag to prevent multiple initializations
    window.phoneAuthInitialized = false;
    
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM Content Loaded on login page');
        
        // Prevent any auto-redirects on the login page
        if (window.location.pathname === '/login') {
            console.log('On login page, disabling auto-redirects');
            // This is handled in the updated checkAuthState function
        }
        
        // Add debug information
        console.log('Firebase SDK loaded:', typeof firebase !== 'undefined');
        if (typeof firebase !== 'undefined') {
            console.log('Firebase Auth loaded:', typeof firebase.auth !== 'undefined');
            console.log('Firebase config:', typeof firebaseConfig !== 'undefined' ? 'Available' : 'Not available');
        } else {
            console.error('Firebase SDK not loaded properly');
        }
        
        // Wait a bit to ensure Firebase is fully loaded, but only initialize once
        if (!window.phoneAuthInitialized) {
            setTimeout(function() {
                try {
                    // This will be initialized in auth.js
                    console.log('Initializing Phone Auth...');
                    initializePhoneAuth();
                    window.phoneAuthInitialized = true;
                    console.log('Phone Auth initialized successfully');
                } catch (error) {
                    console.error('Error initializing Phone Auth:', error);
                }
            }, 1000);
        } else {
            console.log('Phone Auth already initialized, skipping');
        }
    });
</script>
{% endblock %}
