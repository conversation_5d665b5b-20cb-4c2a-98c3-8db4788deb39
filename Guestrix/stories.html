<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stories Inspired by Guests Like You - Guestrix</title>
    <meta name="description" content="Real-life scenarios where <PERSON>cee steps in to help hosts and guests. See how our AI concierge handles everything from emergencies to simple questions.">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="images/favicon.ico">
    <link rel="icon" type="image/png" sizes="32x32" href="images/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="images/favicon-16x16.png">
    <link rel="apple-touch-icon" sizes="180x180" href="images/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="192x192" href="images/android-chrome-192x192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="images/android-chrome-512x512.png">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@400;500;600;700;800&family=Noto+Sans:wght@400;500;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Styles -->
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="logo-container">
                <a href="index.html" class="logo-link">
                    <img src="images/guestrix_logo.svg" alt="Guestrix Logo" class="logo-image">
                    <h2 class="brand-name">Guestrix</h2>
                </a>
            </div>
            <div class="nav-links">
                <a href="index.html" class="nav-link">Home</a>
                <a href="about.html" class="nav-link">About Us</a>
                <a href="pricing.html" class="nav-link">Pricing</a>
                <a href="stories.html" class="nav-link active">Stories Inspired by Guests Like You</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-reduced">
        <div class="container">
            <div class="hero-content">
                <h1 class="hero-title">Stories Inspired by Guests Like You</h1>
                <p class="hero-description">
                    Real-life scenarios where Staycee steps in to help hosts and guests
                </p>
            </div>
        </div>
    </section>

    <!-- Story 1: The Graduation Day Save -->
    <section class="story-section">
        <div class="container">
            <div class="two-column">
                <div class="column-left">
                    <img src="images/graduation-day.png" alt="Family at graduation ceremony" class="section-image">
                </div>
                <div class="column-right">
                    <h3 class="story-title">The Graduation Day Save</h3>
                    <p class="story-text">
                        Todd, a short-term rental host, is attending his son's graduation. He forwards all guest calls to Staycee so he can stay fully present with his family.
                    </p>
                    <p class="story-text">
                        That morning, his guests Mary and Ken are making breakfast when their toddler Ben knocks a glass onto the floor. Mary quickly grabs Ben to keep him safe while Ken searches for a broom but can't find one. He calls Todd's number, and Staycee picks up.
                    </p>
                    <p class="story-text">
                        Staycee calmly tells Ken the broom is in the kitchen closet and notifies Todd with a quick text update. Later, Todd checks in with the guests and hears everything was handled smoothly.
                    </p>
                    <p class="story-highlight">
                        The guests felt supported, and Todd never had to leave the ceremony.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Story 2: The Flood Fix -->
    <section class="story-section alt">
        <div class="container">
            <div class="two-column">
                <div class="column-left">
                    <h3 class="story-title">The Flood Fix</h3>
                    <p class="story-text">
                        Krishna manages three short-term rentals, and it's a particularly hectic weekend. To stay sane, he forwards all guest calls to Staycee.
                    </p>
                    <p class="story-text">
                        One guest, Julie, checks in and hops in the shower. Minutes later, the bathroom begins to flood. She panics and calls Krishna.
                    </p>
                    <p class="story-text">
                        Staycee answers immediately, walks Julie through shutting off the water main, and sends Krishna a detailed message and live call transfer so he can follow up.
                    </p>
                    <p class="story-highlight">
                        Julie stays calm, the issue is resolved quickly, and Krishna avoids a major emergency.
                    </p>
                </div>
                <div class="column-right">
                    <img src="images/flood-emergency.png" alt="Emergency response and problem solving" class="section-image">
                </div>
            </div>
        </div>
    </section>

    <!-- Story 3: Finals Week Rescue -->
    <section class="story-section">
        <div class="container">
            <div class="two-column">
                <div class="column-left">
                    <img src="images/student-studying.png" alt="Student studying during finals week" class="section-image">
                </div>
                <div class="column-right">
                    <h3 class="story-title">Finals Week Rescue</h3>
                    <p class="story-text">
                        Clara is a full-time student who rents out her condo. During finals week, she forwards guest calls to Staycee to focus on her exams.
                    </p>
                    <p class="story-text">
                        Her guest, Harris, arrives flustered after travel delays. He can't find the building and is running late for a business meeting.
                    </p>
                    <p class="story-text">
                        He calls Clara, and Staycee answers right away. She guides him to the gray condo across from Walgreens and confirms his check-in.
                    </p>
                    <p class="story-highlight">
                        Harris gets in with time to spare, Clara stays focused on her studies, and both are relieved.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Story 4: The VIP Check-In Call -->
    <section class="story-section alt">
        <div class="container">
            <div class="two-column">
                <div class="column-left">
                    <h3 class="story-title">The VIP Check-In Call</h3>
                    <p class="story-text">
                        Luis manages multiple listings and uses Staycee to streamline guest communication.
                    </p>
                    <p class="story-text">
                        Late one night, an elderly couple arrives and calls for help with the smart lock. Staycee walks them through the instructions but senses ongoing confusion and growing anxiety in their voices.
                    </p>
                    <p class="story-text">
                        Recognizing this moment calls for a personal touch, Staycee notifies Luis and connects the call to him directly.
                    </p>
                    <p class="story-highlight">
                        Staycee knew when to escalate and gave Luis the chance to shine.
                    </p>
                </div>
                <div class="column-right">
                    <img src="images/elderly-couple-checkin.png" alt="Elderly couple checking into vacation rental" class="section-image">
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
        <div class="container">
            <div class="hero-content">
                <h2 class="hero-subtitle">Ready to create your own Staycee stories?</h2>
                <p class="hero-description">
                    Join hosts who are already saving time and providing better guest experiences.
                </p>
                
                <div class="hero-cta">
                    <a href="pricing.html#contact" class="cta-button">
                        <span>Get Started Today</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <img src="images/guestrix_logo.svg" alt="Guestrix Logo" class="footer-logo-image">
                    <h3 class="footer-brand-name">Guestrix</h3>
                </div>
                <div class="footer-links">
                    <a href="index.html">Home</a>
                    <a href="about.html">About Us</a>
                    <a href="pricing.html">Pricing</a>
                    <a href="stories.html">Stories Inspired by Guests Like You</a>
                </div>
                <div class="footer-contact">
                    <p>&copy; 2025 Guestrix. All rights reserved.</p>
                    <p>Contact: <a href="mailto:<EMAIL>"><EMAIL></a></p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="config.js" type="module"></script>
    <script src="script.js" type="module"></script>
</body>
</html>
