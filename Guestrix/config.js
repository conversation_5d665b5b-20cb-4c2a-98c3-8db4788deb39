const config = {
    api: {
        endpoint: 'https://fx6ti5wvm9.execute-api.us-east-2.amazonaws.com/dev'
    },
    // Removed insecure gemini.apiKey configuration - now using ephemeral tokens
    security: {
        // For production, ephemeral tokens are fetched from the server
        // For development, API keys can be stored in localStorage
        useEphemeralTokens: true
    }
};

// SECURITY NOTE: API keys are no longer exposed in the client-side code
// In production, ephemeral tokens are fetched from the server endpoint
// In development, API keys can be stored in localStorage for convenience

console.log("Security configuration loaded - using ephemeral tokens for production");

export default config; 