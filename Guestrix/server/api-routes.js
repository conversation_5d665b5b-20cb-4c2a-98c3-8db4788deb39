/**
 * API routes for Guestrix server
 */
const express = require('express');
const router = express.Router();

// Environment variables
const GEMINI_API_KEY = process.env.GEMINI_API_KEY;
const VOICE_CALL_ENABLED = process.env.VOICE_CALL_ENABLED !== 'false'; // Default to true unless explicitly disabled

// Route to check if voice call feature is enabled
router.get('/voice-call-status', (req, res) => {
    res.json({
        enabled: VOICE_CALL_ENABLED,
        message: VOICE_CALL_ENABLED ? 
            "Voice call feature is enabled" : 
            "Voice call feature is disabled in this environment"
    });
});

// Route to generate ephemeral tokens for Gemini Live API
router.post('/ephemeral-token', async (req, res) => {
    // Check if voice call feature is enabled
    if (!VOICE_CALL_ENABLED) {
        return res.status(503).json({
            error: "Voice call feature disabled",
            message: "Voice call functionality is disabled in this environment"
        });
    }
    // In production, you should add authentication here
    // For example, check for valid session/JWT token
    
    if (!GEMINI_API_KEY) {
        console.error("Gemini API key not configured on server");
        return res.status(500).json({ 
            error: "API key not configured",
            message: "Voice call functionality is currently unavailable"
        });
    }
    
    try {
        // Calculate expiration times
        const now = new Date();
        const expireTime = new Date(now.getTime() + 30 * 60 * 1000); // 30 minutes from now
        const newSessionExpireTime = new Date(now.getTime() + 1 * 60 * 1000); // 1 minute from now
        
        // Create AuthToken request according to the API documentation
        const authTokenRequest = {
            authToken: {
                uses: 1,
                expireTime: expireTime.toISOString(),
                newSessionExpireTime: newSessionExpireTime.toISOString(),
                bidiGenerateContentSetup: {
                    model: "models/gemini-2.0-flash-live-001",
                    generationConfig: {
                        temperature: 0.7,
                        responseModalities: ["AUDIO", "TEXT"]
                    },
                    realtimeInputConfig: {
                        automaticActivityDetection: {}
                    }
                }
            }
        };
        
        console.log('Making request to create ephemeral token...');
        console.log('Request body:', JSON.stringify(authTokenRequest, null, 2));
        
        const response = await fetch('https://generativelanguage.googleapis.com/v1beta/authTokens', {
            method: 'POST',
            headers: {
                'x-goog-api-key': GEMINI_API_KEY,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(authTokenRequest)
        });
        
        if (!response.ok) {
            const errorText = await response.text();
            console.error('Ephemeral token API error:', response.status, response.statusText, errorText);
            console.log('Ephemeral tokens API not available, falling back to development mode');
            
            // Fallback for development/testing - return a secure session identifier
            // In production, this should never be used
            const isLocalDev = req.get('host')?.includes('localhost') || req.get('host')?.includes('127.0.0.1');
            
            if (isLocalDev && process.env.NODE_ENV !== 'production') {
                console.log('Development mode: Using temporary session approach');
                // Create a temporary session ID for local testing
                const sessionId = `dev_session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
                return res.json({
                    sessionId: sessionId,
                    message: "Development mode: Using temporary session for local testing",
                    expiresAt: expireTime.toISOString(),
                    useDirectApiKey: true, // Flag for frontend to use localStorage key in dev
                    warning: "This is NOT secure for production use"
                });
            }
            
            throw new Error(`API request failed: ${response.status} ${response.statusText} - ${errorText}`);
        }
        
        const tokenData = await response.json();
        console.log('Ephemeral token created successfully');
        
        // Return the ephemeral token name (this is what the client uses as the API key)
        res.json({
            token: tokenData.name,
            expiresAt: expireTime.toISOString(),
            newSessionExpiresAt: newSessionExpireTime.toISOString()
        });
        
    } catch (error) {
        console.error('Error creating ephemeral token:', error);
        res.status(500).json({ 
            error: "Failed to create ephemeral token",
            message: "Voice call functionality is temporarily unavailable",
            details: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});

// Keep the legacy endpoint for backwards compatibility, but log a warning
router.get('/gemini-config', (req, res) => {
    console.warn("WARNING: /gemini-config endpoint is deprecated and insecure. Use /ephemeral-token instead.");
    
    if (!GEMINI_API_KEY) {
        console.error("Gemini API key not configured on server");
        return res.status(500).json({ 
            error: "API key not configured",
            message: "Voice call functionality is currently unavailable"
        });
    }
    
    // Return the API key securely
    res.json({
        apiKey: GEMINI_API_KEY
    });
});

module.exports = router; 