/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Image Optimization */
img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* Lazy loading placeholder */
img[loading="lazy"] {
    opacity: 0;
    transition: opacity 0.3s ease;
}

img[loading="lazy"].loaded {
    opacity: 1;
}

:root {
    /* Brand Colors from Guestrix Dashboard */
    --persian-green: #2a9d8f;
    --saffron: #e9c46a;
    --dark-purple: #161032;
    --light-cyan: #e0fbfc;
    --bittersweet: #ee6055;

    /* Additional colors for design */
    --white: #ffffff;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
}

body {
    font-family: 'Plus Jakarta Sans', 'Noto Sans', -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: var(--dark-purple);
    background-color: var(--white);
    position: relative;
    overflow-x: hidden;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

@media (min-width: 768px) {
    .container {
        padding: 0 2rem;
    }
}

/* Navigation */
.navbar {
    background-color: var(--white);
    padding: 1rem 0;
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(22, 16, 50, 0.1);
    backdrop-filter: blur(10px);
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

@media (min-width: 768px) {
    .nav-container {
        padding: 0 2rem;
    }
}

.logo-container {
    display: flex;
    align-items: center;
}

.logo-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    text-decoration: none;
    transition: opacity 0.3s ease;
}

.logo-image {
    height: 60px;
    width: auto;
    transition: transform 0.3s ease;
    object-fit: contain;
}

.logo-link:hover {
    opacity: 0.85;
}

.logo-image:hover {
    transform: scale(1.05);
}

.brand-name {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--dark-purple);
    margin: 0;
    letter-spacing: -0.02em;
}

.nav-links {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.nav-link {
    color: var(--dark-purple);
    text-decoration: none;
    font-weight: 600;
    font-size: 0.95rem;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.2s ease;
    position: relative;
}

.nav-link:hover {
    color: var(--persian-green);
    background-color: rgba(42, 157, 143, 0.1);
}

.nav-link.active {
    color: var(--persian-green);
    background-color: rgba(42, 157, 143, 0.15);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 2px;
    background-color: var(--persian-green);
    border-radius: 1px;
}

.nav-cta-button {
    background: var(--persian-green);
    color: var(--white);
    text-decoration: none;
    font-weight: 600;
    font-size: 0.95rem;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(42, 157, 143, 0.2);
    margin-left: 1rem;
}

.nav-cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(42, 157, 143, 0.3);
    background: var(--saffron);
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg,
        var(--light-cyan) 0%,
        rgba(42, 157, 143, 0.23) 25%,
        rgba(233, 196, 106, 0.19) 50%,
        rgba(238, 96, 85, 0.13) 75%,
        rgba(22, 16, 50, 0.08) 100%);
    padding: 120px 0 80px;
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
}

/* Reduced Hero Section for About, Stories, and Pricing pages */
.hero-reduced {
    background: linear-gradient(135deg,
        var(--light-cyan) 0%,
        rgba(42, 157, 143, 0.23) 25%,
        rgba(233, 196, 106, 0.19) 50%,
        rgba(238, 96, 85, 0.13) 75%,
        rgba(22, 16, 50, 0.08) 100%);
    padding: 100px 0 0;
    min-height: 26vh;
    display: flex;
    align-items: center;
    position: relative;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 400"><defs><pattern id="rental-icons" width="400" height="400" patternUnits="userSpaceOnUse"><g fill="%23161032" opacity="0.06"><g transform="translate(50,50) scale(0.8)"><path d="M2 6h20v2H2zm2 3h16v11H4zm2 2v7h12V11zm2 1h8v1H8zm0 2h8v1H8zm0 2h5v1H8z"/></g><g transform="translate(180,80) scale(1.2)"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/></g><g transform="translate(320,120) scale(0.6)"><path d="M18.5 4l1.16 4.35-.81.51c-.47.3-.85.66-1.13 1.06L16 12l2 2-1.72 1.72c.28.4.66.76 1.13 1.06l.81.51L17.06 20H6.94l-1.16-4.35.81-.51c.47-.3.85-.66 1.13-1.06L8 12l-2-2 1.72-1.72c-.28-.4-.66-.76-1.13-1.06l-.81-.51L6.94 4h11.56z"/></g><g transform="translate(80,200) scale(1.0)"><path d="M9 11H7v6a2 2 0 002 2h8v-2H9v-6zm3-7C8.48 4 5 7.48 5 11v6H3l4 4 4-4H9v-6c0-1.66 1.34-3 3-3s3 1.34 3 3h2c0-3.52-3.48-7-7-7z"/></g><g transform="translate(250,250) scale(0.9)"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/></g><g transform="translate(150,30) scale(0.7)"><path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/></g><g transform="translate(30,150) scale(1.1)"><path d="M7 14c-1.66 0-3 1.34-3 3 0 1.31.84 2.41 2 2.83V22h2v-2.17c1.16-.42 2-1.52 2-2.83 0-1.66-1.34-3-3-3zM7 18c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1z"/></g><g transform="translate(350,200) scale(0.5)"><path d="M21 3H3c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h5l-1 2v1h8v-1l-1-2h5c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 12H3V5h18v10z"/></g><g transform="translate(120,280) scale(0.8)"><path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/></g><g transform="translate(280,40) scale(0.9)"><path d="M14 6V4h-4v2h4zM4 8v11h16V8H4zm16-2c1.11 0 2 .89 2 2v11c0 1.11-.89 2-2 2H4c-1.11 0-2-.89-2-2l.01-11c0-1.11.88-2 1.99-2h4V4c0-1.11.89-2 2-2h4c1.11 0 2 .89 2 2v2h4z"/></g><g transform="translate(200,180) scale(0.7)"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/></g><g transform="translate(60,320) scale(1.0)"><path d="M9.5 16c-2.49 0-4.5-2.01-4.5-4.5S7.01 7 9.5 7s4.5 2.01 4.5 4.5S11.99 16 9.5 16zM9.5 9C8.12 9 7 10.12 7 11.5S8.12 14 9.5 14s2.5-1.12 2.5-2.5S10.88 9 9.5 9z"/></g></g></pattern></defs><rect width="400" height="400" fill="url(%23rental-icons)"/></svg>');
    pointer-events: none;
}

.hero-content {
    text-align: center;
    max-width: 800px;
    margin: 3rem auto;
    position: relative;
    z-index: 1;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    color: var(--dark-purple);
    margin-bottom: 1.5rem;
    line-height: 1.1;
    letter-spacing: -0.02em;
}

.hero-subtitle {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: 1.5rem;
    line-height: 1.3;
}

.hero-subtitle strong {
    color: var(--persian-green);
}

.hero-description {
    font-size: 1.25rem;
    color: var(--gray-600);
    margin-bottom: 2rem;
    line-height: 1.6;
}

/* Color Highlights */
.highlight-bittersweet {
    color: var(--bittersweet);
}

.highlight-green {
    color: var(--persian-green);
}

/* Voice Call Button */
.hero-cta {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.voice-call-btn {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    background: var(--saffron);
    color: var(--dark-purple);
    border: none;
    padding: 1.25rem 2.5rem;
    border-radius: 50px;
    font-size: 1.125rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(233, 196, 106, 0.4);
    position: relative;
}

.voice-call-btn::before {
    content: '';
    position: absolute;
    inset: -3px;
    background: linear-gradient(45deg, var(--bittersweet), var(--saffron), var(--bittersweet), var(--saffron));
    background-size: 400% 400%;
    border-radius: 53px;
    z-index: -1;
    animation: gradientBorder 3s ease infinite;
}

.voice-call-btn::after {
    content: '';
    position: absolute;
    inset: 0;
    background: var(--saffron);
    border-radius: 50px;
    z-index: -1;
}

@keyframes gradientBorder {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

.voice-call-btn:hover {
    background: #f4d03f;
    color: var(--dark-purple);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(244, 208, 63, 0.5);
}

.voice-call-btn:hover::before {
    animation-duration: 1.5s;
    background: linear-gradient(45deg, var(--saffron), var(--bittersweet), var(--saffron), var(--bittersweet));
    background-size: 400% 400%;
}

.voice-call-btn:hover::after {
    background: #f4d03f;
}

.voice-call-btn[data-status="active"] {
    background: var(--bittersweet);
    color: var(--white);
    animation: pulse-active 2s infinite;
}

.voice-call-btn[data-status="active"]::before {
    animation-duration: 1s;
    background: linear-gradient(45deg, var(--bittersweet), var(--dark-purple), var(--bittersweet), var(--dark-purple));
    background-size: 400% 400%;
}

.voice-call-btn[data-status="active"]::after {
    background: var(--bittersweet);
}

.voice-call-btn[data-status="active"] .btn-text::after {
    content: 'End Call';
}

.voice-call-btn[data-status="active"] .btn-text {
    font-size: 0;
}

@keyframes pulse-active {
    0%, 100% {
        box-shadow: 0 4px 15px rgba(238, 96, 85, 0.3);
    }
    50% {
        box-shadow: 0 4px 25px rgba(238, 96, 85, 0.6);
    }
}

.call-disclaimers {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
}

.disclaimer-text {
    font-size: 0.875rem;
    color: var(--gray-600);
    text-align: center;
    margin: 0;
}

/* Section Layouts */
.problem-section,
.why-staycee,
.social-proof,
.cta-section {
    padding: 80px 0;
}

.how-it-works {
    padding: 80px 0;
    background-color: var(--gray-50);
}

.two-column {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.column-left,
.column-right {
    display: flex;
    flex-direction: column;
}

.section-image {
    width: 100%;
    height: auto;
    min-height: 300px;
    max-height: 500px;
    object-fit: cover;
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(22, 16, 50, 0.1);
    aspect-ratio: 16/15;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-purple);
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.section-title.centered {
    text-align: center;
}

.section-subtitle {
    font-size: 1.25rem;
    color: var(--gray-600);
    text-align: center;
    margin-bottom: 3rem;
    line-height: 1.6;
}

.section-text {
    font-size: 1.125rem;
    color: var(--gray-700);
    margin-bottom: 1.5rem;
    line-height: 1.7;
}

/* Features Grid */
.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    margin-top: 3rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.feature-card {
    background: var(--white);
    padding: 2rem;
    border-radius: 16px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(22, 16, 50, 0.08);
    transition: all 0.3s ease;
    border: 1px solid rgba(42, 157, 143, 0.1);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(22, 16, 50, 0.15);
    border-color: var(--persian-green);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--persian-green), var(--saffron));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: var(--white);
    font-size: 1.5rem;
}

.feature-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--dark-purple);
    margin-bottom: 1rem;
}

.feature-description {
    font-size: 1rem;
    color: var(--gray-600);
    line-height: 1.6;
}

/* Testimonials */
.testimonial-highlight {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--dark-purple);
    font-style: italic;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: rgba(42, 157, 143, 0.1);
    border-left: 4px solid var(--persian-green);
    border-radius: 0 8px 8px 0;
    line-height: 1.6;
}

.testimonial-footer {
    margin-top: 1.5rem;
    margin-left: 2rem;
}

.testimonial-highlight .testimonial-author {
    font-size: 1rem;
    color: var(--gray-600);
    font-style: normal;
    font-weight: 500;
    margin-bottom: 1rem;
}

.testimonial-highlight .stats {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    font-style: normal;
}

.testimonial-highlight .stat {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.stats {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.stat {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--persian-green);
    line-height: 1;
}

.stat-label {
    font-size: 0.95rem;
    color: var(--gray-600);
    margin-top: 0.25rem;
}

/* New Testimonials Grid Layout */
.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-top: 3rem;
    margin-bottom: 3rem;
}

.testimonial-card {
    background: var(--white);
    padding: 2rem;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(22, 16, 50, 0.08);
    transition: all 0.3s ease;
    border: 1px solid rgba(42, 157, 143, 0.1);
    position: relative;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(22, 16, 50, 0.15);
    border-color: var(--persian-green);
}

.testimonial-content {
    font-size: 1rem;
    font-weight: 400;
    color: var(--dark-purple);
    font-style: italic;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    position: relative;
}

.testimonial-content::before {
    content: '"';
    font-size: 3rem;
    color: var(--persian-green);
    position: absolute;
    top: -1rem;
    left: -0.5rem;
    font-family: Georgia, serif;
    opacity: 0.3;
}

.testimonial-author {
    font-size: 0.9rem;
    color: var(--gray-600);
    font-style: normal;
    font-weight: 600;
    margin-top: 1rem;
    margin-left: 1.5rem;
}

/* Overall Stats Section */
.overall-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin-top: 4rem;
    padding: 2.5rem;
    background: rgba(42, 157, 143, 0.05);
    border-radius: 16px;
    border: 1px solid rgba(42, 157, 143, 0.1);
}

.overall-stats .stat {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex-direction: row;
}

.stat-icon {
    font-size: 1.25rem;
    color: var(--persian-green);
    flex-shrink: 0;
}

.stat-text {
    font-size: 1rem;
    font-weight: 600;
    color: var(--dark-purple);
    line-height: 1.4;
}

/* Responsive Design for Testimonials */
@media (max-width: 1024px) {
    .testimonials-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        margin-top: 2rem;
        margin-bottom: 2rem;
    }
    
    .testimonial-card {
        padding: 1.5rem;
    }
    
    .overall-stats {
        flex-direction: column;
        gap: 1.5rem;
        margin-top: 2.5rem;
        padding: 2rem;
    }
    
    .overall-stats .stat {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .testimonial-content {
        font-size: 0.95rem;
    }
    
    .testimonial-content::before {
        font-size: 2.5rem;
        top: -0.75rem;
        left: -0.25rem;
    }
    
    .testimonial-author {
        font-size: 0.85rem;
    }
    
    .overall-stats {
        padding: 1.5rem;
        margin-top: 2rem;
    }
    
    .stat-text {
        font-size: 0.9rem;
        text-align: center;
    }
}

/* Step-based layout for About page */
.step-item {
    margin-bottom: 2rem;
}

.step-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--dark-purple);
    margin-bottom: 0.75rem;
    line-height: 1.4;
}

.step-description {
    font-size: 1rem;
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: 0;
}

@media (max-width: 768px) {
    .step-item {
        margin-bottom: 1.5rem;
    }
    
    .step-title {
        font-size: 1rem;
    }
    
    .step-description {
        font-size: 0.95rem;
    }
}

/* Forms */
.waitlist-form {
    background: var(--saffron);
    padding: 2.5rem;
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(22, 16, 50, 0.1);
    border: 1px solid rgba(42, 157, 143, 0.1);
}

.form-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--dark-purple);
    margin-bottom: 1.5rem;
    text-align: center;
}

.form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group input {
    padding: 1rem;
    border: 2px solid var(--gray-200);
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.2s ease;
    background: var(--white);
    color: var(--dark-purple);
}

.form-group input:focus {
    outline: none;
    border-color: var(--persian-green);
    box-shadow: 0 0 0 3px rgba(42, 157, 143, 0.1);
}

.form-submit-btn {
    background: var(--persian-green);
    color: var(--white);
    border: none;
    padding: 1rem 2rem;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.form-submit-btn:hover {
    background: rgba(42, 157, 143, 0.9);
    transform: translateY(-1px);
}

/* Footer */
.footer {
    background: var(--dark-purple);
    color: var(--white);
    padding: 3rem 0 2rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr;
    gap: 2rem;
    align-items: center;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.footer-logo-image {
    height: 40px;
    width: auto;
    /* Apply white color to footer logo */
    filter: brightness(0) saturate(100%) invert(100%);
}

.footer-brand-name {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--white);
    margin: 0;
    letter-spacing: -0.02em;
}

.footer-links {
    display: flex;
    justify-content: center;
    gap: 2rem;
}

.footer-links a {
    color: var(--light-cyan);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
}

.footer-links a:hover {
    color: var(--saffron);
}

.footer-contact {
    text-align: right;
}

.footer-contact p {
    margin: 0.25rem 0;
    color: var(--light-cyan);
    font-size: 0.9rem;
}

.footer-contact a {
    color: var(--light-cyan);
    text-decoration: none;
}

.footer-contact a:hover {
    color: var(--white);
    text-decoration: none;
}

/* Additional Page Styles */

/* About Page */
.feature-list {
    list-style: none;
    padding: 0;
    margin: 1.5rem 0;
}

.feature-list li {
    padding: 0.5rem 0;
    color: var(--gray-700);
    font-size: 1.125rem;
    position: relative;
    padding-left: 1.5rem;
}

.feature-list li::before {
    content: '•';
    color: var(--persian-green);
    font-weight: bold;
    position: absolute;
    left: 0;
}

.technology-section {
    padding: 80px 0;
    background-color: var(--light-cyan);
}

.closing-section {
    padding: 80px 0;
    background: linear-gradient(135deg, var(--dark-purple), var(--persian-green));
    color: var(--white);
}

.closing-section .hero-subtitle {
    color: var(--white);
}

.cta-button {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--saffron);
    color: var(--dark-purple);
    text-decoration: none;
    padding: 1rem 2rem;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.2s ease;
    margin-top: 1rem;
}

.cta-button:hover {
    background: rgba(233, 196, 106, 0.9);
    transform: translateY(-1px);
}

/* Pricing Page - Consolidated styles */
.pricing-section {
    padding: 80px 0;
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.pricing-card {
    background: var(--white);
    border-radius: 16px;
    padding: 2.5rem;
    box-shadow: 0 4px 20px rgba(22, 16, 50, 0.08);
    border: 2px solid transparent;
    transition: all 0.3s ease;
    position: relative;
}

.pricing-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(22, 16, 50, 0.15);
    border-color: var(--persian-green);
}

/* Removed featured pricing card styles to make all cards equal
.pricing-card.featured {
    border-color: var(--persian-green);
    transform: scale(1.05);
}

.pricing-card.featured::before {
    content: 'Most Popular';
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--persian-green);
    color: var(--white);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
}
*/

.plan-header {
    text-align: center;
    margin-bottom: 2rem;
}

.plan-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--dark-purple);
    margin-bottom: 1rem;
}

.plan-price {
    margin-bottom: 1rem;
}

.price-amount {
    font-size: 3rem;
    font-weight: 800;
    color: var(--persian-green);
}

.price-period {
    font-size: 1rem;
    color: var(--gray-600);
}

.plan-description {
    color: var(--gray-600);
    font-size: 1rem;
}

.plan-features {
    list-style: none;
    padding: 0;
    margin: 2rem 0;
}

.plan-features li {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 0;
    color: var(--gray-700);
    border-bottom: 1px solid var(--gray-100);
}

.plan-features li:last-child {
    border-bottom: none;
}

.plan-features i {
    color: var(--persian-green);
    font-size: 1rem;
}

.plan-example {
    background: var(--gray-50);
    padding: 1rem;
    border-radius: 8px;
    margin: 1.5rem 0;
    font-size: 0.95rem;
    color: var(--gray-700);
}

.plan-cta-btn {
    width: 100%;
    background: var(--persian-green);
    color: var(--white);
    border: none;
    padding: 1rem;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.plan-cta-btn:hover {
    background: rgba(42, 157, 143, 0.9);
}

.custom-pricing-section {
    padding: 80px 0;
    background-color: var(--gray-50);
}

/* Additional Components */
.feature-list {
    list-style: none;
    padding: 0;
    margin: 1.5rem 0;
}

.feature-list li {
    padding: 0.5rem 0;
    font-size: 1.125rem;
    color: var(--gray-700);
    line-height: 1.6;
}

.feature-list li::before {
    content: '✔️';
    margin-right: 0.75rem;
    color: var(--persian-green);
}

.technology-section,
.closing-section {
    padding: 80px 0;
}

.closing-section {
    background: linear-gradient(135deg, var(--light-cyan) 0%, var(--white) 100%);
}

.cta-button {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--persian-green);
    color: var(--white);
    text-decoration: none;
    padding: 1.25rem 2.5rem;
    border-radius: 50px;
    font-size: 1.125rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(42, 157, 143, 0.3);
}

.cta-button:hover {
    background: rgba(42, 157, 143, 0.9);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(42, 157, 143, 0.4);
}

/* Removed duplicate pricing section styles - consolidated above */

/* Removed duplicate pricing card styles - using the ones above */

/* Removed duplicate featured pricing card styles
.pricing-card.featured {
    border-color: var(--persian-green);
    transform: scale(1.05);
}

.pricing-card.featured::before {
    content: 'Most Popular';
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--persian-green);
    color: var(--white);
    padding: 0.5rem 1.5rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
}
*/

.pricing-header {
    text-align: center;
    margin-bottom: 2rem;
}

.plan-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--dark-purple);
    margin-bottom: 1rem;
}

.plan-price {
    margin-bottom: 1rem;
}

.price-amount {
    font-size: 3rem;
    font-weight: 800;
    color: var(--persian-green);
    line-height: 1;
}

.price-period {
    font-size: 1rem;
    color: var(--gray-600);
    margin-left: 0.5rem;
}

.plan-description {
    font-size: 1rem;
    color: var(--gray-600);
    line-height: 1.6;
}

.pricing-features {
    margin-bottom: 2rem;
}

.features-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.features-list li {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 0.75rem 0;
    font-size: 1rem;
    color: var(--gray-700);
    line-height: 1.6;
}

.features-list li i {
    color: var(--persian-green);
    margin-top: 0.25rem;
    flex-shrink: 0;
}

.pricing-example {
    background: var(--light-cyan);
    padding: 1rem;
    border-radius: 8px;
    margin-top: 1.5rem;
    font-size: 0.95rem;
    color: var(--dark-purple);
}

.pricing-footer {
    text-align: center;
}

.pricing-cta {
    display: inline-block;
    background: var(--dark-purple);
    color: var(--white);
    text-decoration: none;
    padding: 1rem 2rem;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.2s ease;
    width: 100%;
    text-align: center;
}

.pricing-cta:hover {
    background: rgba(22, 16, 50, 0.9);
    transform: translateY(-1px);
}

.custom-pricing {
    padding: 80px 0;
    background: linear-gradient(135deg, var(--dark-purple) 0%, rgba(22, 16, 50, 0.9) 100%);
    color: var(--white);
}

.custom-pricing .section-title {
    color: var(--white);
}

.custom-pricing .section-text {
    color: var(--light-cyan);
}

.custom-pricing .form-group textarea {
    resize: vertical;
    min-height: 80px;
}

/* Stories Page Styles */
.story-section {
    padding: 80px 0;
}

.story-section.alt {
    background-color: var(--gray-50);
}

.story-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--dark-purple);
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.story-text {
    font-size: 1.125rem;
    color: var(--gray-700);
    margin-bottom: 1.5rem;
    line-height: 1.7;
}

.story-highlight {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--persian-green);
    font-style: italic;
    padding: 1rem;
    background: rgba(42, 157, 143, 0.1);
    border-left: 4px solid var(--persian-green);
    border-radius: 0 8px 8px 0;
}

/* Success Modal */
.success-modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(22, 16, 50, 0.8);
    backdrop-filter: blur(5px);
}

.success-modal-content {
    background: var(--white);
    margin: 10% auto;
    padding: 3rem;
    border-radius: 20px;
    width: 90%;
    max-width: 500px;
    text-align: center;
    box-shadow: 0 20px 60px rgba(22, 16, 50, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.success-icon {
    margin-bottom: 1.5rem;
}

.success-icon i {
    font-size: 4rem;
    color: var(--persian-green);
}

.success-modal-content h3 {
    font-size: 2rem;
    color: var(--dark-purple);
    margin-bottom: 1rem;
    font-weight: 600;
}

.success-modal-content p {
    font-size: 1.1rem;
    color: var(--dark-purple);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.success-modal-close {
    background: linear-gradient(135deg, var(--persian-green), var(--saffron));
    color: var(--white);
    border: none;
    padding: 1rem 2.5rem;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(42, 157, 143, 0.3);
}

.success-modal-close:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(42, 157, 143, 0.4);
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-container {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
    }

    .nav-links {
        gap: 1rem;
        flex-wrap: wrap;
        justify-content: center;
    }

    .hero {
        padding: 100px 0 60px;
        min-height: auto;
    }

    .hero-reduced {
        padding: 26px 0 0;
        min-height: auto;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.25rem;
    }

    .hero-description {
        font-size: 1.125rem;
    }

    .voice-call-btn {
        padding: 1rem 2rem;
        font-size: 1rem;
    }

    .two-column {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .section-text {
        font-size: 1rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        max-width: none;
    }

    .pricing-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    /* Removed featured card responsive styles
    .pricing-card.featured {
        transform: none;
    }
    */

    .footer-content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        text-align: center;
    }

    .footer-links {
        flex-direction: column;
        gap: 1rem;
    }

    .footer-contact {
        text-align: center;
    }

    .waitlist-form {
        padding: 1.5rem;
    }

    /* Removed duplicate responsive pricing grid styles */

    .section-image {
        object-position: center +40%;
        min-height: 250px;
        max-height: 350px;
        aspect-ratio: 16/15;
    }

    /* Removed duplicate featured card responsive styles
    .pricing-card.featured {
        transform: none;
    }
    */

    .plan-price {
        margin-bottom: 1.5rem;
    }

    .price-amount {
        font-size: 2.5rem;
    }

    .price-period {
        display: block;
        margin-left: 0;
        margin-top: 0.5rem;
    }
}

/* About page styles */
.about-section {
    max-width: 900px;
    padding: 3rem 2rem;
    margin: 0 auto;
    background-color: rgba(255, 255, 255, 0.92);
    backdrop-filter: blur(15px);
    border-radius: 25px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.about-section:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.about-section h1 {
    color: #000;
    margin-bottom: 2.5rem;
    text-align: center;
    font-family: 'Playfair Display', serif;
    font-size: 2.8rem;
    font-weight: 700;
    letter-spacing: -0.5px;
}

.about-content {
    padding: 2rem;
    border-radius: 15px;
}

.about-content h2 {
    font-family: 'Playfair Display', serif;
    color: #000;
    margin: 2.5rem 0 1.2rem;
    font-size: 2.2rem;
    font-weight: 700;
    letter-spacing: -0.3px;
    line-height: 1.3;
}

.about-content h2:first-of-type {
    margin-top: 1.5rem;
}

.about-content p {
    margin-bottom: 1.5rem;
    color: #333;
    font-size: 1.15rem;
    line-height: 1.8;
}

.about-content p:first-of-type {
    font-size: 1.3rem;
    line-height: 1.9;
    color: #222;
    font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .navbar {
        padding: 0.75rem;
        flex-wrap: wrap;
        position: relative; /* Allow normal flow on mobile */
        justify-content: center;
    }

    .subtitle {
        margin-left: 0;
        margin-top: 0.5rem;
        font-size: 1rem;
        text-align: center;
    }

    .nav-links {
        gap: 0.5rem;
        margin-top: 0.5rem;
        width: 100%;
        justify-content: center;
    }

    main {
        padding-top: 0rem;
    }

    .hero-section {
        padding: 2rem 1.5rem;
    }

    .hero-section h1 {
        font-size: 2.5rem;
        line-height: 1.3;
    }

    .hero-section .description {
        font-size: 1.125rem;
    }

    .tagline {
        font-size: 1.2rem;
    }

    .talk-button {
        padding: 1.2rem 2rem;
        font-size: 1.1rem;
    }

    .about-content h2 {
        font-size: 2rem;
        margin: 2rem 0 1rem;
    }

    .about-content p:first-of-type {
        font-size: 1.2rem;
    }

    .hero-heading {
        font-size: 2.5rem;
    }

    .logo-image {
        height: 80px; /* Slightly smaller but maintains aspect ratio */
        max-width: 100%; /* Ensures the logo fits in the container */
    }
    
    .logo-tagline {
        font-size: 0.9rem;
    }

    .success-modal-content {
        margin: 20% auto;
        padding: 2rem;
        width: 95%;
    }

    .success-modal-content h3 {
        font-size: 1.5rem;
    }

    .success-icon i {
        font-size: 3rem;
    }

    .success-modal-close {
        padding: 0.875rem 2rem;
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .navbar {
        flex-direction: column;
        gap: 0.5rem;
        padding: 0.5rem;
    }

    .subtitle {
        margin: 0.25rem 0;
        text-align: center;
    }

    .nav-links {
        flex-wrap: wrap;
        justify-content: center;
        gap: 0.25rem;
    }

    .nav-cta-button {
        margin-left: 0;
        margin-top: 0.5rem;
        padding: 0.6rem 1.2rem;
        font-size: 0.9rem;
    }

    .hero-section {
        padding: 2rem 1.25rem;
        margin-top: 1rem;
    }

    .hero-section h1 {
        font-size: 1.8rem;
        line-height: 1.3;
    }

    .hero-section .description {
        font-size: 1rem;
    }

    .tagline {
        font-size: 1.1rem;
        margin-bottom: 2rem;
    }

    .talk-button {
        padding: 1rem 1.5rem;
        font-size: 1rem;
    }

    .about-section {
        padding: 2rem 1.25rem;
    }

    .about-section h1 {
        font-size: 2rem;
    }

    .about-content h2 {
        font-size: 1.7rem;
        margin: 1.5rem 0 0.8rem;
    }

    .about-content p:first-of-type {
        font-size: 1.1rem;
    }

    .hero-heading {
        font-size: 1.8rem;
    }

    .logo-image {
        height: 65px;
        width: auto; /* Maintain aspect ratio */
    }
    
    .logo-tagline {
        font-size: 0.8rem;
    }

    .logo-container {
        display: flex;
        align-items: center;
    }
}

/* Removed third pricing section styles - using consolidated styles above
.pricing-section {
    max-width: 1200px;
    margin: 4rem auto;
    padding: 0 2rem;
    text-align: center;
}

.pricing-section h1 {
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #fff;
}

.pricing-cards {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.pricing-card {
    background: #fff;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    padding: 2rem;
    width: 350px;
    transition: transform 0.3s ease;
}

.pricing-card:hover {
    transform: translateY(-5px);
}
*/

/* Removed additional featured card styles
.pricing-card.featured {
    border: 2px solid #000;
    position: relative;
}
*/

/* Removed third pricing section related styles - using consolidated styles above
.card-header {
    margin-bottom: 2rem;
}

.card-header h2 {
    font-size: 1.8rem;
    margin-bottom: 1rem;
    color: #000;
}

.price {
    font-size: 1.2rem;
    color: #333;
}

.price .amount {
    font-size: 3rem;
    font-weight: bold;
    color: #000;
}

.price .period {
    color: #666;
}

.card-content ul {
    list-style: none;
    padding: 0;
    margin: 2rem 0;
    text-align: left;
}

.card-content li {
    margin: 1rem 0;
    color: #333;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.card-content li i {
    color: #000;
}

.pricing-cta {
    background-color: #000;
    color: white;
    border: none;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    border-radius: 50px;
    cursor: pointer;
    width: 100%;
    transition: all 0.3s ease;
    margin-top: 1rem;
}

.pricing-cta:hover {
    background-color: #333;
    transform: translateY(-2px);
}
*/

/* Removed responsive styles for third pricing section - using consolidated styles above
@media (max-width: 768px) {
    .pricing-card {
        width: 100%;
        max-width: 400px;
    }
}
*/

/* Stories Section Styles */
.stories-section {
    max-width: 1200px;
    margin: 4rem auto;
    padding: 0 2rem;
}

.stories-section h1 {
    font-size: 2.5rem;
    color: #fff;
    text-align: center;
    margin-bottom: 1rem;
}

.stories-intro {
    text-align: center;
    color: #fff;
    font-size: 1.2rem;
    margin-bottom: 4rem;
}

.story-card {
    display: flex;
    gap: 3rem;
    margin-bottom: 4rem;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.story-content {
    flex: 1;
}

.story-image {
    flex: 1;
    display: flex;
    align-items: center;
}

.story-image img {
    width: 100%;
    height: auto;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.story-card h2 {
    font-size: 1.8rem;
    color: #000;
    margin-bottom: 1.5rem;
}

.story-text p {
    color: #333;
    line-height: 1.6;
    margin-bottom: 1rem;
}

@media (max-width: 768px) {
    .story-card {
        flex-direction: column;
    }

    .story-image {
        order: -1;
    }

    .story-card h2 {
        font-size: 1.5rem;
    }
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background-color: white;
    padding: 2rem;
    border-radius: 12px;
    max-width: 500px;
    width: 90%;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    position: relative;
}

.modal-content h2 {
    margin-bottom: 1rem;
    color: var(--text-color);
}

.modal-content p {
    margin-bottom: 1.5rem;
    color: var(--text-color);
}

#waitlistForm {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    position: relative;
}

#waitlistForm input {
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 1rem;
    width: 100%;
    box-sizing: border-box;
    transition: border-color 0.2s ease;
}

#waitlistForm input:focus {
    outline: none;
    border-color: var(--primary-color);
}

#waitlistForm input:invalid {
    border-color: #ff4444;
}

#waitlistForm input:valid {
    border-color: #4CAF50;
}

#waitlistForm input::placeholder {
    color: #999;
}

.form-group::after {
    content: '*';
    color: #ff4444;
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1.2rem;
}

.form-group.optional::after {
    display: none;
}

#waitlistForm input:valid + .form-group::after {
    color: #4CAF50;
    content: '✓';
}

.modal-button {
    background-color: #b51614;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s ease;
    width: 100%;
    margin-top: 1rem;
}

.modal-button:hover {
    background-color: #8d110f;
}

.modal-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-color);
}

.modal-close:hover {
    color: var(--primary-color);
}

@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        padding: 1.5rem;
    }
}

/* Accessibility */
.visually-hidden {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.skip-link {
    position: absolute;
    top: -40px;
    left: 0;
    background: #000;
    color: white;
    padding: 8px;
    z-index: 100;
    transition: top 0.3s;
}

.skip-link:focus {
    top: 0;
}

/* Focus styles */
button:focus,
a:focus,
input:focus {
    outline: 2px solid #4A90E2;
    outline-offset: 2px;
}

/* Performance optimizations */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

img {
    max-width: 100%;
    height: auto;
}

/* Preload critical resources */
@font-face {
    font-family: 'Inter';
    font-display: swap;
    src: url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
}

@font-face {
    font-family: 'Playfair Display';
    font-display: swap;
    src: url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&display=swap');
}

/* Voice Call Modal Styles */
.voice-call-modal {
    max-width: 450px;
}

.voice-call-interface {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 20px 0;
}

.audio-visualizer {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
}

.audio-bars {
    display: flex;
    justify-content: center;
    align-items: flex-end;
    height: 40px;
    margin: 20px 0;
}

.audio-bar {
    width: 4px;
    height: 5px;
    background-color: #6600cc;
    border-radius: 2px;
    margin: 0 2px;
    transition: height 0.2s ease;
}

.audio-bar.active {
    animation: audio-wave 1.2s ease-in-out infinite;
}

@keyframes audio-wave {
    0%, 100% { height: 5px; }
    50% { height: 30px; }
}

.audio-bar:nth-child(1) { animation-delay: -1.2s; }
.audio-bar:nth-child(2) { animation-delay: -1.0s; }
.audio-bar:nth-child(3) { animation-delay: -0.8s; }
.audio-bar:nth-child(4) { animation-delay: -0.6s; }
.audio-bar:nth-child(5) { animation-delay: -0.4s; }
.audio-bar:nth-child(6) { animation-delay: -0.2s; }
.audio-bar:nth-child(7) { animation-delay: 0s; }

.call-status {
    margin-top: 10px;
    font-size: 16px;
    color: #333;
    text-align: center;
}

.call-controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 20px;
}

.call-button {
    padding: 10px 20px;
    border-radius: 30px;
    border: none;
    background-color: #6600cc;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 100px;
}

.call-button:hover {
    background-color: #5500aa;
}

.call-button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
}

.end-call {
    background-color: #ff3366;
}

.end-call:hover {
    background-color: #e62e5c;
}

.call-disclaimer {
    font-size: 10px;
    color: #666;
    text-align: center;
    margin-top: 20px;
    max-width: 80%;
    margin-left: auto;
    margin-right: auto;
}

/* FAQ Section Styles */
.faq-section {
    padding: 80px 0;
    background-color: var(--background-light);
}

.faq-grid {
    max-width: 800px;
    margin: 40px auto 0;
}

.faq-item {
    background: white;
    border-radius: 12px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.faq-item:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.faq-question {
    padding: 24px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 16px;
}

.faq-question h4 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    flex: 1;
}

.faq-question i {
    color: var(--text-secondary);
    transition: transform 0.3s ease;
}

.faq-item.active .faq-question i {
    transform: rotate(180deg);
}

.faq-answer {
    padding: 0 24px;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
    opacity: 0;
}

.faq-item.active .faq-answer {
    padding: 0 24px 24px;
    max-height: 500px;
    opacity: 1;
}

.faq-answer p {
    margin: 0;
    color: var(--text-secondary);
    line-height: 1.6;
}

@media (max-width: 768px) {
    .faq-section {
        padding: 60px 0;
    }

    .faq-question h4 {
        font-size: 1rem;
    }

    .faq-question {
        padding: 20px;
    }

    .faq-item.active .faq-answer {
        padding: 0 20px 20px;
    }
}