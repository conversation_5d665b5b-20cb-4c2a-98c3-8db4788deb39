# Testing Ephemeral Tokens Implementation

## 🔐 What We've Implemented

We've successfully implemented a secure ephemeral token system that eliminates the security vulnerability of exposing Gemini API keys in client-side code.

### Security Improvements
- **Before**: API key was exposed in browser source code and network requests
- **After**: Ephemeral tokens are generated server-side, with fallback for development

## 🧪 Testing Instructions

### Prerequisites
1. Ensure you have a Gemini API key available
2. Make sure the server dependencies are installed: `cd Guestrix/server && npm install`

### Step 1: Start the Server
```bash
cd Guestrix/server
node server.js
```

You should see:
```
Fetching parameters from SSM...
Server running on port 8082
```

### Step 2: Test Ephemeral Token Endpoint
```bash
curl -X POST -H "Content-Type: application/json" http://localhost:8082/api/ephemeral-token -s | jq .
```

**Expected Response** (Development Mode):
```json
{
  "sessionId": "dev_session_1750166051092_v5901cg0w",
  "message": "Development mode: Using temporary session for local testing",
  "expiresAt": "2025-06-17T13:44:10.882Z",
  "useDirectApiKey": true,
  "warning": "This is NOT secure for production use"
}
```

### Step 3: Test Frontend Integration
1. Open http://localhost:8082 in your browser
2. Click the voice call button
3. When prompted, enter your Gemini API key (will be stored in localStorage)
4. Test the voice functionality

### Step 4: Browser Developer Tools Verification

**Security Check**:
1. Open browser Developer Tools (F12)
2. Go to Network tab
3. Clear previous requests
4. Click the voice call button
5. **Verify**: No API key is visible in any network requests
6. **Verify**: Only session IDs or ephemeral tokens are transmitted

## 🔍 What to Test

### Development Mode Features
- [ ] API key prompt appears for first-time users
- [ ] API key is stored in localStorage for subsequent uses
- [ ] Voice call works with stored API key
- [ ] No API key exposed in network requests

### Security Features
- [ ] Browser source code contains no hardcoded API keys
- [ ] Network requests show no API keys in headers or body
- [ ] WebSocket connection uses proper authentication method

### Fallback Behavior
- [ ] Server gracefully handles ephemeral token API unavailability
- [ ] Development mode activated when ephemeral tokens fail
- [ ] Clear warning messages displayed in console

## 🚨 Production Considerations

### When Ephemeral Tokens Become Available
1. The server will automatically use real ephemeral tokens
2. No frontend code changes needed
3. Fallback mode will not activate in production

### Production Deployment
1. Set `NODE_ENV=production` to disable development fallbacks
2. Ensure proper authentication is added to the `/api/ephemeral-token` endpoint
3. Configure proper CORS and security headers

## 📊 Testing Results Expected

### ✅ Success Indicators
- Server starts without errors
- Ephemeral token endpoint returns fallback response
- Frontend prompts for API key in development
- Voice call functionality works
- No API keys visible in browser tools

### ❌ Failure Indicators  
- Server fails to start
- API key visible in network requests
- Voice call fails with authentication errors
- Browser console shows security warnings

## 🔧 Troubleshooting

### Common Issues
1. **Server won't start**: Check if port 8082 is available
2. **Voice call fails**: Verify API key is valid
3. **No audio**: Check browser microphone permissions
4. **WebSocket errors**: Verify API key and network connection

### Debug Commands
```bash
# Check if server is running
ps aux | grep "node server" | grep -v grep

# Test API endpoint directly
curl -X POST http://localhost:8082/api/ephemeral-token -v

# Check server logs for errors
# (Look at the terminal where you started the server)
```

## 📈 Performance Notes

- Development mode adds one additional network request to fetch the fallback token
- Production mode will use ephemeral tokens when available
- WebSocket connection performance is identical to previous implementation

## 🔒 Security Benefits Achieved

1. **API Key Protection**: Real API key never reaches the browser
2. **Token Expiration**: Ephemeral tokens expire automatically (30 min max)
3. **Limited Usage**: Tokens can be configured for single-use sessions
4. **Development Safety**: Clear warnings about insecure development practices
5. **Future-Proof**: Ready for when Google makes ephemeral tokens publicly available 