# Ephemeral Tokens Security Implementation

## Overview

This implementation replaces the insecure practice of embedding Gemini API keys directly in client-side code with a secure ephemeral token system. This significantly improves security by ensuring that long-lived API keys never reach the browser.

## Security Benefits

1. **No API Key Exposure**: The actual Gemini API key never reaches the browser
2. **Short-Lived Tokens**: Ephemeral tokens expire quickly (30 minutes maximum)
3. **Limited Usage**: Tokens can be configured for single-use sessions
4. **Constrained Permissions**: Tokens can be locked to specific model configurations

## How It Works

### Production Environment

1. **Client Request**: <PERSON><PERSON><PERSON> requests an ephemeral token from your backend
2. **Server Creates Token**: Backend server uses the real API key to generate an ephemeral token via Gemini API
3. **Token Delivery**: Server returns the ephemeral token to the client
4. **Secure Usage**: Client uses the ephemeral token as if it were an API key for WebSocket connections

### Development Environment

For local development convenience, the system falls back to localStorage API key storage.

## Implementation Details

### Backend Changes

- **New Endpoint**: `/api/ephemeral-token` (POST) - Creates ephemeral tokens
- **Legacy Endpoint**: `/api/gemini-config` (GET) - Deprecated but still available
- **Dependencies**: Added `@google/genai` package for token creation

### Frontend Changes

- **Secure Token Fetching**: `fetchEphemeralToken()` function for production
- **Environment Detection**: Automatic switching between development and production modes
- **WebSocket Authentication**: Supports both API keys (dev) and ephemeral tokens (prod)
- **Config Cleanup**: Removed insecure API key exposure from config.js

## Setup Instructions

### 1. Install Dependencies

```bash
cd server
npm install
```

### 2. Environment Variables

Ensure your server has the `GEMINI_API_KEY` environment variable set:

```bash
export GEMINI_API_KEY="your_actual_gemini_api_key_here"
```

### 3. Start the Server

```bash
cd server
npm start
```

### 4. Development Setup (Optional)

For local development, you can still use localStorage:

1. Open browser console on your local site
2. Run: `localStorage.setItem('GEMINI_API_KEY', 'your_dev_api_key')`
3. The system will automatically use this for development

## Token Configuration

The ephemeral tokens are configured with the following security constraints:

- **Expiration**: 30 minutes from creation
- **New Session Window**: 1 minute to start new sessions
- **Usage Limit**: Single use (can start only one session)
- **Model Lock**: Restricted to `gemini-2.0-flash-live-001`
- **Response Modes**: Audio and text responses only

## Monitoring and Debugging

### Server Logs

The server logs when ephemeral tokens are created:
```
Ephemeral token created successfully
```

### Client Logs

The browser console shows which authentication method is being used:
```
Using ephemeral token for WebSocket connection
# or
Using API key for WebSocket connection (development)
```

## Security Best Practices

1. **Never commit API keys** to version control
2. **Use environment variables** for server-side API keys
3. **Implement authentication** on the ephemeral token endpoint in production
4. **Monitor token usage** for unusual patterns
5. **Rotate API keys regularly**

## Migration from Previous Implementation

The system maintains backward compatibility:

1. **Legacy endpoint** still works but logs deprecation warnings
2. **Development mode** unchanged - still uses localStorage
3. **Production automatically** switches to ephemeral tokens

## Troubleshooting

### Common Issues

1. **"Failed to create ephemeral token"**
   - Check server logs for detailed error
   - Verify GEMINI_API_KEY is set correctly
   - Ensure server can reach Gemini API

2. **"Authentication token not available"**
   - Check network connectivity
   - Verify server is running and accessible
   - Check browser console for fetch errors

3. **WebSocket connection fails**
   - Verify token format (should start with `auth_tokens/`)
   - Check if token has expired
   - Ensure using correct API version (v1beta)

### Debug Mode

Enable debug logging by adding to browser console:
```javascript
localStorage.setItem('DEBUG_VOICE_CALL', 'true');
```

## API Reference

### Ephemeral Token Endpoint

**POST** `/api/ephemeral-token`

**Response:**
```json
{
  "token": "auth_tokens/...",
  "expiresAt": "2025-01-15T10:30:00Z",
  "newSessionExpiresAt": "2025-01-15T10:01:00Z"
}
```

**Error Response:**
```json
{
  "error": "Failed to create ephemeral token",
  "message": "Detailed error message"
}
``` 