version: 1
applications:
  - frontend:
      phases:
        preBuild:
          commands:
            - npm install
        build:
          commands:
            - echo "API_ENDPOINT=https://d1u169ifu6z79x.execute-api.us-east-2.amazonaws.com/dev" >> .env
            - echo "GEMINI_API_KEY=${GEMINI_API_KEY}" >> .env
            - npm run build
            - node inject-env.js
      artifacts:
        baseDirectory: dist
        files:
          - '**/*'
      cache:
        paths:
          - node_modules/**/*
      customHeaders:
        - pattern: '**/*'
          headers:
            - key: 'Strict-Transport-Security'
              value: 'max-age=31536000; includeSubDomains'
            - key: 'X-Content-Type-Options'
              value: 'nosniff'
            - key: 'X-XSS-Protection'
              value: '1; mode=block'
